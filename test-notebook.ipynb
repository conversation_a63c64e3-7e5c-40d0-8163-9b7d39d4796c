{"cells": [{"cell_type": "markdown", "id": "37343bbf", "metadata": {}, "source": ["# MSF Data Engineering Technical Test - Development Notebook\n", "\n", "This notebook serves as a development and testing environment for building the data pipeline as required by the Ennonce Technical Test documentation.\n", "\n", "It will include:\n", "* Iterative code development and testing for each layer of the Medallion Architecture (Bronze, Silver, Gold).\n", "* Helper functions and snippets used during development.\n", "* Validation steps to ensure data quality and transformations are correct.\n", "\n", "Once stable, the production-ready code will be refactored written to `main.py`.\n", "\n", "Refer to the \"Ennonce_Technical Test – Data Engineer.docx\" for full requirements and context."]}, {"cell_type": "code", "execution_count": null, "id": "57ec55f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "3deee6cb", "metadata": {}, "outputs": [], "source": ["# Bronze layer\n", "# get the bronze dataframes (budget dataframe and the expenses)\n"]}, {"cell_type": "code", "execution_count": null, "id": "2361f7c1", "metadata": {}, "outputs": [], "source": ["\n", "# This dictionary contains the TRUTH for project country and currency,\n", "# overriding any inconsistencies found in source data.\n", "PROJECT_CURRENCY_MAP = {\n", "    \"BE01\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "    \"BE55\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "    \"KE01\": {\"country\": \"Kenya\", \"currency\": \"KES\"},   # Corrected: Force KES for KE01 expenses, overriding DB's XOF\n", "    \"KE02\": {\"country\": \"Kenya\", \"currency\": \"KES\"},\n", "    \"SN01\": {\"country\": \"Senegal\", \"currency\": \"XOF\"}, # Corrected: Force Senegal for SN01 country, overriding DB's Kenya\n", "    \"SN02\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "    \"BF01\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "    \"BF02\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "}"]}, {"cell_type": "code", "execution_count": 20, "id": "4e220f0b", "metadata": {}, "outputs": [], "source": ["DATA_DIRECTORY = \"/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025\"\n", "OUTPUT_DIRECTORY = \"/Users/<USER>/test/MSF-test/processed_data\""]}, {"cell_type": "code", "execution_count": 21, "id": "9604c735", "metadata": {}, "outputs": [], "source": ["# Read csvs (budget data)\n", "import pandas as pd\n", "import os\n", "\n", "# --- Function to extract budget from a single CSV ---\n", "def extract_budget_from_csv(csv_path: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Reads budget data from a CSV file.\n", "    \"\"\"\n", "    try:\n", "        df = pd.read_csv(csv_path)\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Error reading budget from {csv_path}: {e}\")\n", "        return pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": 236, "id": "479837b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Bronze Layer: Starting CSV Data Ingestion from /Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025 ---\n", "project_name_from_file: BE55_budget\n", "  Ingesting budget from CSV: BE55_budget.csv (Project: BE55, Country: Belgium)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     2293.00      v1\n", "1   2  2023      1         HR           Training     4768.76      v1\n", "2   3  2023      1         HR        Recruitment     1642.21      v1\n", "3   4  2023      1    Medical        Medications     1377.75      v1\n", "4   5  2023      1    Medical  Medical Equipment     2062.46      v1\n", "project_name_from_file: SN02_budget\n", "  Ingesting budget from CSV: SN02_budget.csv (Project: SN02, Country: Senegal)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     4046.14      v1\n", "1   2  2023      1         HR           Training     2020.66      v1\n", "2   3  2023      1         HR        Recruitment     3237.90      v1\n", "3   4  2023      1    Medical        Medications     3282.13      v1\n", "4   5  2023      1    Medical  Medical Equipment     4840.12      v1\n", "project_name_from_file: BF01_budget\n", "  Ingesting budget from CSV: BF01_budget.csv (Project: BF01, Country: Burkina Faso)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     2684.05      v1\n", "1   2  2023      1         HR           Training     3178.92      v1\n", "2   3  2023      1         HR        Recruitment     4459.01      v1\n", "3   4  2023      1    Medical        Medications     2960.56      v1\n", "4   5  2023      1    Medical  Medical Equipment     3841.15      v1\n", "project_name_from_file: BE01_budget\n", "  Ingesting budget from CSV: BE01_budget.csv (Project: BE01, Country: Belgium)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     3059.10      v1\n", "1   2  2023      1         HR           Training     3080.06      v1\n", "2   3  2023      1         HR        Recruitment     2695.93      v1\n", "3   4  2023      1    Medical        Medications     3000.73      v1\n", "4   5  2023      1    Medical  Medical Equipment     3353.35      v1\n", "project_name_from_file: BF02_budget\n", "  Ingesting budget from CSV: BF02_budget.csv (Project: BF02, Country: Burkina Faso)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     1447.57      v1\n", "1   2  2023      1         HR           Training     3876.89      v1\n", "2   3  2023      1         HR        Recruitment     3599.19      v1\n", "3   4  2023      1    Medical        Medications     2722.46      v1\n", "4   5  2023      1    Medical  Medical Equipment     2535.48      v1\n", "project_name_from_file: SN01_budget\n", "  Ingesting budget from CSV: SN01_budget.csv (Project: SN01, Country: Senegal)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     4329.90      v1\n", "1   2  2023      1         HR           Training     3936.97      v1\n", "2   3  2023      1         HR        Recruitment     1153.74      v1\n", "3   4  2023      1    Medical        Medications     4980.65      v1\n", "4   5  2023      1    Medical  Medical Equipment     3560.72      v1\n", "project_name_from_file: KE01_budget\n", "  Ingesting budget from CSV: KE01_budget.csv (Project: KE01, Country: Kenya)...\n", "df: \n", "   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     2507.85      v1\n", "1   2  2023      1         HR           Training     4373.79      v1\n", "2   3  2023      1         HR        Recruitment     4486.83      v1\n", "3   4  2023      1    Medical        Medications     1169.57      v1\n", "4   5  2023      1    Medical  Medical Equipment     2022.73      v1\n", "project_name_from_file: KEO2_budget\n", "Warning: Project 'KEO2' (from 'KEO2_budget.csv') not found in PROJECT_CURRENCY_MAP. Skipping.\n"]}], "source": ["# --- Main logic to iterate and combine CSVs ---\n", "all_budget_df = pd.DataFrame()\n", "csv_files_found = 0\n", "\n", "print(f\"\\n--- Bronze Layer: Starting CSV Data Ingestion from {DATA_DIRECTORY} ---\")\n", "\n", "if not os.path.exists(DATA_DIRECTORY):\n", "    print(f\"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.\")\n", "else:\n", "    for filename in os.listdir(DATA_DIRECTORY):\n", "        file_path = os.path.join(DATA_DIRECTORY, filename)\n", "        \n", "        # Process only files ending with '_budget.csv' and skip directories\n", "        if os.path.isdir(file_path) or not filename.endswith(\"_budget.csv\"):\n", "            continue\n", "\n", "        # Derive project_id from filename (e.g., \"BE01\" from \"BE01_budget.csv\")\n", "        project_name_from_file = os.path.splitext(filename)[0]\n", "        print(f\"project_name_from_file: {project_name_from_file}\")\n", "        base_project_id = project_name_from_file.replace('_budget', '')\n", "\n", "        # Get country from our PROJECT_CURRENCY_MAP\n", "        project_info = PROJECT_CURRENCY_MAP.get(base_project_id)\n", "        if not project_info:\n", "            print(f\"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.\")\n", "            continue\n", "        \n", "        country_from_map = project_info['country']\n", "\n", "        print(f\"  Ingesting budget from CSV: {filename} (Project: {base_project_id}, Country: {country_from_map})...\")\n", "        df = extract_budget_from_csv(file_path)\n", "        print(f\"df: \\n{df.head()}\")\n", "        \n", "        if not df.empty:\n", "            df['project_id'] = base_project_id\n", "            df['country'] = country_from_map\n", "            # Also add the original_currency_map for consistency, even if budget is already in EUR\n", "            all_budget_df = pd.concat([all_budget_df, df], ignore_index=True)\n", "            csv_files_found += 1\n", "        else:\n", "            print(f\" No data extracted or error occurred for {filename}.\")\n"]}, {"cell_type": "code", "execution_count": 23, "id": "4c06ab87", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['BE01', 'BE55', 'KE01', 'KEO2', 'SN01', 'SN02', 'BF01', 'BF02'])\n"]}], "source": ["print(PROJECT_CURRENCY_MAP.keys())"]}, {"cell_type": "code", "execution_count": 24, "id": "15a0e4a9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year  month department           category  budget_eur version  \\\n", "0   1  2023      1         HR           Salaries     2293.00      v1   \n", "1   2  2023      1         HR           Training     4768.76      v1   \n", "2   3  2023      1         HR        Recruitment     1642.21      v1   \n", "3   4  2023      1    Medical        Medications     1377.75      v1   \n", "4   5  2023      1    Medical  Medical Equipment     2062.46      v1   \n", "\n", "  project_id  country  \n", "0       BE55  Belgium  \n", "1       BE55  Belgium  \n", "2       BE55  Belgium  \n", "3       BE55  Belgium  \n", "4       BE55  Belgium  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["all_budget_df.head()"]}, {"cell_type": "code", "execution_count": 26, "id": "d91006e1", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame\n", "df_filtered = all_budget_df[all_budget_df['project_id'] == 'KEO2']\n"]}, {"cell_type": "code", "execution_count": 28, "id": "b44dd802", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3024</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>1331.85</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3025</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4093.98</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3026</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>3510.71</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3027</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>3764.67</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3028</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2855.33</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  year  month department           category  budget_eur version  \\\n", "3024   1  2023      1         HR           Salaries     1331.85      v1   \n", "3025   2  2023      1         HR           Training     4093.98      v1   \n", "3026   3  2023      1         HR        Recruitment     3510.71      v1   \n", "3027   4  2023      1    Medical        Medications     3764.67      v1   \n", "3028   5  2023      1    Medical  Medical Equipment     2855.33      v1   \n", "\n", "     project_id country  \n", "3024       KEO2   Kenya  \n", "3025       KEO2   Kenya  \n", "3026       KEO2   Kenya  \n", "3027       KEO2   Kenya  \n", "3028       KEO2   Kenya  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df_filtered.head()"]}, {"cell_type": "code", "execution_count": 25, "id": "2953cd0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["3456"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["len(all_budget_df)"]}, {"cell_type": "code", "execution_count": 11, "id": "5919ad4c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>3059.10</td>\n", "      <td>v1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>3080.06</td>\n", "      <td>v1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>2695.93</td>\n", "      <td>v1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>3000.73</td>\n", "      <td>v1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>3353.35</td>\n", "      <td>v1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year  month department           category  budget_eur version\n", "0   1  2023      1         HR           Salaries     3059.10      v1\n", "1   2  2023      1         HR           Training     3080.06      v1\n", "2   3  2023      1         HR        Recruitment     2695.93      v1\n", "3   4  2023      1    Medical        Medications     3000.73      v1\n", "4   5  2023      1    Medical  Medical Equipment     3353.35      v1"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_df = pd.read_csv('/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025/BE01_budget.csv')\n", "temp_df.head()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "85630b12", "metadata": {}, "outputs": [{"data": {"text/plain": ["432"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["len(temp_df)"]}, {"cell_type": "code", "execution_count": 30, "id": "295a802c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Bronze Budget DataFrame saved to: /Users/<USER>/test/MSF-test/processed_data/bronze_budget_data.parquet\n"]}], "source": ["# save the bronze budget\n", "# Define the output path for the bronze budget data\n", "bronze_budget_output_path = os.path.join(OUTPUT_DIRECTORY, \"bronze_budget_data.parquet\")\n", "\n", "if not all_budget_df.empty:\n", "    all_budget_df.to_parquet(bronze_budget_output_path, index=False)\n", "    print(f\"\\nBronze Budget DataFrame saved to: {bronze_budget_output_path}\")\n", "else:\n", "    print(\"\\nBronze Budget DataFrame is empty, skipping save to Parquet.\")"]}, {"cell_type": "code", "execution_count": 32, "id": "d7f570d4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>428</td>\n", "      <td>2025</td>\n", "      <td>12</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>4017.93</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>429</td>\n", "      <td>2025</td>\n", "      <td>12</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>2099.59</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>430</td>\n", "      <td>2025</td>\n", "      <td>12</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>4371.15</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>431</td>\n", "      <td>2025</td>\n", "      <td>12</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>2842.26</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>432</td>\n", "      <td>2025</td>\n", "      <td>12</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>4898.71</td>\n", "      <td>v1</td>\n", "      <td>KEO2</td>\n", "      <td>Kenya</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       id  year  month department             category  budget_eur version  \\\n", "3451  428  2025     12  Logistics            Transport     4017.93      v1   \n", "3452  429  2025     12  Logistics  Vehicle Maintenance     2099.59      v1   \n", "3453  430  2025     12     Supply             Supplies     4371.15      v1   \n", "3454  431  2025     12     Supply          Warehousing     2842.26      v1   \n", "3455  432  2025     12     Supply           Cold Chain     4898.71      v1   \n", "\n", "     project_id country  \n", "3451       KEO2   Kenya  \n", "3452       KEO2   Kenya  \n", "3453       KEO2   Kenya  \n", "3454       KEO2   Kenya  \n", "3455       KEO2   Kenya  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["all_budget_df.tail()"]}, {"cell_type": "code", "execution_count": 38, "id": "0087ce23", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year  month department           category  budget_eur version  \\\n", "0   1  2023      1         HR           Salaries     2293.00      v1   \n", "1   2  2023      1         HR           Training     4768.76      v1   \n", "2   3  2023      1         HR        Recruitment     1642.21      v1   \n", "3   4  2023      1    Medical        Medications     1377.75      v1   \n", "4   5  2023      1    Medical  Medical Equipment     2062.46      v1   \n", "\n", "  project_id  country  \n", "0       BE55  Belgium  \n", "1       BE55  Belgium  \n", "2       BE55  Belgium  \n", "3       BE55  Belgium  \n", "4       BE55  Belgium  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# reading the bronze budget parquet file\n", "bronze_budget_df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/bronze_budget_data.parquet')\n", "bronze_budget_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fcf082d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ef421e78", "metadata": {}, "source": ["## Bronze expenses"]}, {"cell_type": "code", "execution_count": 40, "id": "6551dc55", "metadata": {}, "outputs": [], "source": ["# --- Bronze Layer: Ingesting Expenses from SQLite DBs ---\n", "\n", "import pandas as pd\n", "import sqlite3\n", "import os\n", "\n", "def extract_expenses_from_db(db_path: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Connects to a SQLite database and extracts all data from the 'expenses' table.\n", "    \"\"\"\n", "    conn = None\n", "    try:\n", "        conn = sqlite3.connect(db_path)\n", "        query = \"SELECT * FROM expenses\" # Assumes 'expenses' is the table name\n", "        df = pd.read_sql_query(query, conn)\n", "        return df\n", "    except sqlite3.Error as e:\n", "        print(f\"Error reading expenses from {db_path}: {e}\")\n", "        return pd.DataFrame() # Return empty DataFrame on error\n", "    finally:\n", "        if conn:\n", "            conn.close()"]}, {"cell_type": "code", "execution_count": 44, "id": "4809b18e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Bronze Layer: Starting DB Data Ingestion from /Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025 ---\n", "  Ingesting expenses from DB: BE55.db (Project: BE55, Country: Belgium)...\n", "  Ingesting expenses from DB: BE01.db (Project: BE01, Country: Belgium)...\n", "  Ingesting expenses from DB: BF02.db (Project: BF02, Country: Burkina Faso)...\n", "  Ingesting expenses from DB: KE02.db (Project: KE02, Country: Kenya)...\n", "  Ingesting expenses from DB: SN02.db (Project: SN02, Country: Senegal)...\n", "  Ingesting expenses from DB: SN01.db (Project: SN01, Country: Senegal)...\n", "  Ingesting expenses from DB: KE01.db (Project: KE01, Country: Kenya)...\n", "  Ingesting expenses from DB: BF01.db (Project: BF01, Country: Burkina Faso)...\n"]}], "source": ["# --- Main logic to iterate and combine DBs ---\n", "all_expenses_df = pd.DataFrame()\n", "db_files_found = 0\n", "\n", "# Define project currency map for expenses\n", "PROJECT_CURRENCY_MAP = {\n", "    \"BE01\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "    \"BE55\": {\"country\": \"Belgium\", \"currency\": \"EUR\"},\n", "    \"KE01\": {\"country\": \"Kenya\", \"currency\": \"KES\"},\n", "    \"KE02\": {\"country\": \"Kenya\", \"currency\": \"KES\"},   \n", "    \"SN01\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "    \"SN02\": {\"country\": \"Senegal\", \"currency\": \"XOF\"},\n", "    \"BF01\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "    \"BF02\": {\"country\": \"Burkina Faso\", \"currency\": \"XOF\"},\n", "}\n", "\n", "print(f\"\\n--- Bronze Layer: Starting DB Data Ingestion from {DATA_DIRECTORY} ---\")\n", "\n", "if not os.path.exists(DATA_DIRECTORY):\n", "    print(f\"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.\")\n", "else:\n", "    for filename in os.listdir(DATA_DIRECTORY):\n", "        file_path = os.path.join(DATA_DIRECTORY, filename)\n", "        \n", "        # Process only files ending with '.db' and skip directories\n", "        if os.path.isdir(file_path) or not filename.endswith(\".db\"):\n", "            continue\n", "\n", "        # Derive project_id from filename (e.g., \"BE01\" from \"BE01.db\")\n", "        project_name_from_file = os.path.splitext(filename)[0]\n", "        base_project_id = project_name_from_file # For DBs, the filename directly is the project_id\n", "\n", "        # Get country and currency from our PROJECT_CURRENCY_MAP\n", "        project_info = PROJECT_CURRENCY_MAP.get(base_project_id)\n", "        if not project_info:\n", "            print(f\"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.\")\n", "            continue\n", "        \n", "        country_from_map = project_info['country']\n", "        currency_from_map = project_info['currency'] # This will be our source of truth for currency\n", "\n", "        print(f\"  Ingesting expenses from DB: {filename} (Project: {base_project_id}, Country: {country_from_map})...\")\n", "        df = extract_expenses_from_db(file_path)\n", "        \n", "        if not df.empty:\n", "            df['project_id'] = base_project_id\n", "            df['country'] = country_from_map\n", "            df['original_currency'] = currency_from_map # Add the true currency from our map\n", "            all_expenses_df = pd.concat([all_expenses_df, df], ignore_index=True)\n", "            db_files_found += 1\n", "        else:\n", "            print(f\"    No data extracted or error occurred for {filename}.\")\n"]}, {"cell_type": "code", "execution_count": 45, "id": "be388c4f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>currency</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year month department           category  amount_local currency  \\\n", "0   1  2023    01         HR           Salaries       2278.71      EUR   \n", "1   2  2023    01         HR           Training       4053.43      EUR   \n", "2   3  2023    01         HR        Recruitment       1450.10      EUR   \n", "3   4  2023    01    Medical        Medications       1275.95      EUR   \n", "4   5  2023    01    Medical  Medical Equipment       2182.81      EUR   \n", "\n", "  project_id  country original_currency  amount_eur  \n", "0       BE55  Belgium               EUR         NaN  \n", "1       BE55  Belgium               EUR         NaN  \n", "2       BE55  Belgium               EUR         NaN  \n", "3       BE55  Belgium               EUR         NaN  \n", "4       BE55  Belgium               EUR         NaN  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["all_expenses_df.head()"]}, {"cell_type": "code", "execution_count": 47, "id": "760e0e3c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3456\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 11 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   id                 3456 non-null   int64  \n", " 1   year               3456 non-null   int64  \n", " 2   month              3456 non-null   object \n", " 3   department         3456 non-null   object \n", " 4   category           3456 non-null   object \n", " 5   amount_local       2880 non-null   float64\n", " 6   currency           3456 non-null   object \n", " 7   project_id         3456 non-null   object \n", " 8   country            3456 non-null   object \n", " 9   original_currency  3456 non-null   object \n", " 10  amount_eur         432 non-null    float64\n", "dtypes: float64(2), int64(2), object(7)\n", "memory usage: 297.1+ KB\n"]}], "source": ["print(len(all_expenses_df))\n", "all_expenses_df.info()"]}, {"cell_type": "code", "execution_count": 53, "id": "7419d22b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>currency</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>361</td>\n", "      <td>2025</td>\n", "      <td>07</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td>362</td>\n", "      <td>2025</td>\n", "      <td>07</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>362</th>\n", "      <td>363</td>\n", "      <td>2025</td>\n", "      <td>07</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>364</td>\n", "      <td>2025</td>\n", "      <td>07</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>365</td>\n", "      <td>2025</td>\n", "      <td>07</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  year month department           category  amount_local currency  \\\n", "360  361  2025    07         HR           Salaries           NaN      EUR   \n", "361  362  2025    07         HR           Training           NaN      EUR   \n", "362  363  2025    07         HR        Recruitment           NaN      EUR   \n", "363  364  2025    07    Medical        Medications           NaN      EUR   \n", "364  365  2025    07    Medical  Medical Equipment           NaN      EUR   \n", "\n", "    project_id  country original_currency  amount_eur  \n", "360       BE55  Belgium               EUR         NaN  \n", "361       BE55  Belgium               EUR         NaN  \n", "362       BE55  Belgium               EUR         NaN  \n", "363       BE55  Belgium               EUR         NaN  \n", "364       BE55  Belgium               EUR         NaN  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["mask = (all_expenses_df['year'] == 2025) & (all_expenses_df['month'] == \"07\")\n", "all_expenses_df[mask].head()"]}, {"cell_type": "code", "execution_count": 54, "id": "6d6db0d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DataFrame successfully saved to: /Users/<USER>/test/MSF-test/processed_data/bronze_expenses_data.parquet\n"]}], "source": ["# save the bronze expenses to parquet:\n", "import pandas as pd\n", "import os\n", "\n", "\n", "# Ensure the output directory exists\n", "os.makedirs(OUTPUT_DIRECTORY, exist_ok=True)\n", "\n", "# Define the output path for the bronze expenses data\n", "bronze_expenses_output_path = os.path.join(OUTPUT_DIRECTORY, \"bronze_expenses_data.parquet\")\n", "\n", "if not all_expenses_df.empty:\n", "    all_expenses_df.to_parquet(bronze_expenses_output_path, index=False)\n", "    print(f\"\\nDataFrame successfully saved to: {bronze_expenses_output_path}\")\n", "else:\n", "    print(\"\\nDataFrame is empty, skipping save to Parquet.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "87772ef7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "9162ff96", "metadata": {}, "source": ["## Silver layer"]}, {"cell_type": "code", "execution_count": 106, "id": "7829ad45", "metadata": {}, "outputs": [], "source": ["### Dealing with expenses dataframe\n"]}, {"cell_type": "code", "execution_count": 107, "id": "b25fbef9", "metadata": {}, "outputs": [], "source": ["expenses_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/bronze_expenses_data.parquet\")"]}, {"cell_type": "code", "execution_count": 108, "id": "4be70c7d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>currency</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year month department           category  amount_local currency  \\\n", "0   1  2023    01         HR           Salaries       2278.71      EUR   \n", "1   2  2023    01         HR           Training       4053.43      EUR   \n", "2   3  2023    01         HR        Recruitment       1450.10      EUR   \n", "3   4  2023    01    Medical        Medications       1275.95      EUR   \n", "4   5  2023    01    Medical  Medical Equipment       2182.81      EUR   \n", "\n", "  project_id  country original_currency  amount_eur  \n", "0       BE55  Belgium               EUR         NaN  \n", "1       BE55  Belgium               EUR         NaN  \n", "2       BE55  Belgium               EUR         NaN  \n", "3       BE55  Belgium               EUR         NaN  \n", "4       BE55  Belgium               EUR         NaN  "]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.head()"]}, {"cell_type": "code", "execution_count": 109, "id": "0f317789", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 11 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   id                 3456 non-null   int64  \n", " 1   year               3456 non-null   int64  \n", " 2   month              3456 non-null   object \n", " 3   department         3456 non-null   object \n", " 4   category           3456 non-null   object \n", " 5   amount_local       2880 non-null   float64\n", " 6   currency           3456 non-null   object \n", " 7   project_id         3456 non-null   object \n", " 8   country            3456 non-null   object \n", " 9   original_currency  3456 non-null   object \n", " 10  amount_eur         432 non-null    float64\n", "dtypes: float64(2), int64(2), object(7)\n", "memory usage: 297.1+ KB\n"]}], "source": ["expenses_df.info()"]}, {"cell_type": "code", "execution_count": 110, "id": "157cf35a", "metadata": {}, "outputs": [], "source": ["# Drop the id and currency column\n", "expenses_df.drop(columns=['id', 'currency'], inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "884f161d", "metadata": {}, "outputs": [], "source": ["### Combine year and month to have a date like column and convert to datetime and add the last day of the month\n"]}, {"cell_type": "code", "execution_count": 114, "id": "8ce2c606", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Processing Expenses DataFrame Dates ---\n", "Expenses DataFrame 'date' column created and set to the last day of the month.\n", "\n", "--- Updated Expenses DataFrame Info ---\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 10 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   year               3456 non-null   int64         \n", " 1   month              3456 non-null   object        \n", " 2   department         3456 non-null   object        \n", " 3   category           3456 non-null   object        \n", " 4   amount_local       2880 non-null   float64       \n", " 5   project_id         3456 non-null   object        \n", " 6   country            3456 non-null   object        \n", " 7   original_currency  3456 non-null   object        \n", " 8   amount_eur         432 non-null    float64       \n", " 9   date               3456 non-null   datetime64[ns]\n", "dtypes: datetime64[ns](1), float64(2), int64(1), object(6)\n", "memory usage: 270.1+ KB\n", "\n", "--- Updated Expenses DataFrame Head (with budget_date) ---\n", "   year month department           category  amount_local project_id  country  \\\n", "0  2023    01         HR           Salaries       2278.71       BE55  Belgium   \n", "1  2023    01         HR           Training       4053.43       BE55  Belgium   \n", "2  2023    01         HR        Recruitment       1450.10       BE55  Belgium   \n", "3  2023    01    Medical        Medications       1275.95       BE55  Belgium   \n", "4  2023    01    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur       date  \n", "0               EUR         NaN 2023-01-31  \n", "1               EUR         NaN 2023-01-31  \n", "2               EUR         NaN 2023-01-31  \n", "3               EUR         NaN 2023-01-31  \n", "4               EUR         NaN 2023-01-31  \n"]}], "source": ["import pandas as pd\n", "from pandas.tseries.offsets import MonthEnd # Import MonthEnd for easy last-day-of-month calculation\n", "\n", "\n", "print(\"--- Processing Expenses DataFrame Dates ---\")\n", "\n", "# Step 1: Convert year and month to string format\n", "expenses_df['year_str'] = expenses_df['year'].astype(str)\n", "expenses_df['month_str'] = expenses_df['month'].astype(str)\n", "\n", "# Step 2: Create a temporary date string with the 1st day of the month\n", "# This is a common intermediate step before converting to datetime and then adjusting to month end.\n", "expenses_df['temp_date_str'] = expenses_df['year_str'] + '-' + expenses_df['month_str'].str.zfill(2) + '-01'\n", "# .str.zfill(2) ensures single-digit months (e.g., '1') become '01' for correct parsing\n", "\n", "# Step 3: Convert the temporary date string to datetime objects\n", "expenses_df['date'] = pd.to_datetime(expenses_df['temp_date_str'], errors='coerce')\n", "\n", "# Step 4: Convert to the last day of the month using MonthEnd\n", "expenses_df['date'] = expenses_df['date'] + MonthEnd(1)\n", "\n", "# Handle any rows where date conversion might have failed (e.g., if 'month' was not a valid month)\n", "# You might want to inspect these rows if any NaT (Not a Time) values appear.\n", "if expenses_df['date'].isnull().any():\n", "    print(\"Warning: Some budget date values are NaT (Not a Time) after conversion. Check original year/month data.\")\n", "\n", "print(\"Expenses DataFrame 'date' column created and set to the last day of the month.\")\n", "\n", "# Step 5: Drop the intermediate columns if no longer needed\n", "expenses_df = expenses_df.drop(columns=['year_str', 'month_str', 'temp_date_str'])\n", "\n", "# Display updated info and head to verify\n", "print(\"\\n--- Updated Expenses DataFrame Info ---\")\n", "expenses_df.info()\n", "print(\"\\n--- Updated Expenses DataFrame Head (with budget_date) ---\")\n", "print(expenses_df.head())"]}, {"cell_type": "code", "execution_count": 115, "id": "2b12a56f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   year month department           category  amount_local project_id  country  \\\n", "0  2023    01         HR           Salaries       2278.71       BE55  Belgium   \n", "1  2023    01         HR           Training       4053.43       BE55  Belgium   \n", "2  2023    01         HR        Recruitment       1450.10       BE55  Belgium   \n", "3  2023    01    Medical        Medications       1275.95       BE55  Belgium   \n", "4  2023    01    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur       date  \n", "0               EUR         NaN 2023-01-31  \n", "1               EUR         NaN 2023-01-31  \n", "2               EUR         NaN 2023-01-31  \n", "3               EUR         NaN 2023-01-31  \n", "4               EUR         NaN 2023-01-31  "]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.head()"]}, {"cell_type": "code", "execution_count": 116, "id": "bdebca25", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>2023-01-31</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  department           category  amount_local project_id  country  \\\n", "0         HR           Salaries       2278.71       BE55  Belgium   \n", "1         HR           Training       4053.43       BE55  Belgium   \n", "2         HR        Recruitment       1450.10       BE55  Belgium   \n", "3    Medical        Medications       1275.95       BE55  Belgium   \n", "4    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur       date  \n", "0               EUR         NaN 2023-01-31  \n", "1               EUR         NaN 2023-01-31  \n", "2               EUR         NaN 2023-01-31  \n", "3               EUR         NaN 2023-01-31  \n", "4               EUR         NaN 2023-01-31  "]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["# Drop year and month columns\n", "expenses_df.drop(columns=['year', 'month'], inplace=True)\n", "\n", "expenses_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "846058e4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 118, "id": "96e606a4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur  \n", "0               EUR         NaN  \n", "1               EUR         NaN  \n", "2               EUR         NaN  \n", "3               EUR         NaN  \n", "4               EUR         NaN  "]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["# Rearrange so date is the first column\n", "expenses_df = expenses_df[['date'] + [col for col in expenses_df.columns if col != 'date']]\n", "expenses_df.head()"]}, {"cell_type": "code", "execution_count": 119, "id": "2700f137", "metadata": {}, "outputs": [{"data": {"text/plain": ["3456"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["len(expenses_df)"]}, {"cell_type": "code", "execution_count": 120, "id": "454fe9d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 8 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   date               3456 non-null   datetime64[ns]\n", " 1   department         3456 non-null   object        \n", " 2   category           3456 non-null   object        \n", " 3   amount_local       2880 non-null   float64       \n", " 4   project_id         3456 non-null   object        \n", " 5   country            3456 non-null   object        \n", " 6   original_currency  3456 non-null   object        \n", " 7   amount_eur         432 non-null    float64       \n", "dtypes: datetime64[ns](1), float64(2), object(5)\n", "memory usage: 216.1+ KB\n"]}], "source": ["expenses_df.info()"]}, {"cell_type": "code", "execution_count": 123, "id": "a061d587", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 124, "id": "e888c226", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [date, department, category, amount_local, project_id, country, original_currency, amount_eur]\n", "Index: []"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["mask = expenses_df['amount_eur'] == np.nan\n", "expenses_df[mask].head()"]}, {"cell_type": "code", "execution_count": 125, "id": "fcf61fdf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3024 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           date department             category  amount_local project_id  \\\n", "0    2023-01-31         HR             Salaries       2278.71       BE55   \n", "1    2023-01-31         HR             Training       4053.43       BE55   \n", "2    2023-01-31         HR          Recruitment       1450.10       BE55   \n", "3    2023-01-31    Medical          Medications       1275.95       BE55   \n", "4    2023-01-31    Medical    Medical Equipment       2182.81       BE55   \n", "...         ...        ...                  ...           ...        ...   \n", "3451 2025-12-31  Logistics            Transport           NaN       BF01   \n", "3452 2025-12-31  Logistics  Vehicle Maintenance           NaN       BF01   \n", "3453 2025-12-31     Supply             Supplies           NaN       BF01   \n", "3454 2025-12-31     Supply          Warehousing           NaN       BF01   \n", "3455 2025-12-31     Supply           Cold Chain           NaN       BF01   \n", "\n", "           country original_currency  amount_eur  \n", "0          Belgium               EUR         NaN  \n", "1          Belgium               EUR         NaN  \n", "2          Belgium               EUR         NaN  \n", "3          Belgium               EUR         NaN  \n", "4          Belgium               EUR         NaN  \n", "...            ...               ...         ...  \n", "3451  Burkina Faso               XOF         NaN  \n", "3452  Burkina Faso               XOF         NaN  \n", "3453  Burkina Faso               XOF         NaN  \n", "3454  Burkina Faso               XOF         NaN  \n", "3455  Burkina Faso               XOF         NaN  \n", "\n", "[3024 rows x 8 columns]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df [expenses_df['amount_eur'].isna()]"]}, {"cell_type": "code", "execution_count": 126, "id": "8266b209", "metadata": {}, "outputs": [{"data": {"text/plain": ["3024"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["len(expenses_df [expenses_df['amount_eur'].isna()])"]}, {"cell_type": "code", "execution_count": 129, "id": "22116492", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>864</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>9.340303e+05</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>1423.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>865</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>2.122362e+06</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>3235.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>866</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1.894469e+06</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>2888.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>867</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1.876122e+06</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>2860.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>868</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>1.760707e+06</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>2684.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1291</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>NaN</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>3994.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1292</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>NaN</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>1757.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1293</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>NaN</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>2288.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1294</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>NaN</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>1364.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1295</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>NaN</td>\n", "      <td>BF02</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>2269.23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>432 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           date department             category  amount_local project_id  \\\n", "864  2023-01-31         HR             Salaries  9.340303e+05       BF02   \n", "865  2023-01-31         HR             Training  2.122362e+06       BF02   \n", "866  2023-01-31         HR          Recruitment  1.894469e+06       BF02   \n", "867  2023-01-31    Medical          Medications  1.876122e+06       BF02   \n", "868  2023-01-31    Medical    Medical Equipment  1.760707e+06       BF02   \n", "...         ...        ...                  ...           ...        ...   \n", "1291 2025-12-31  Logistics            Transport           NaN       BF02   \n", "1292 2025-12-31  Logistics  Vehicle Maintenance           NaN       BF02   \n", "1293 2025-12-31     Supply             Supplies           NaN       BF02   \n", "1294 2025-12-31     Supply          Warehousing           NaN       BF02   \n", "1295 2025-12-31     Supply           Cold Chain           NaN       BF02   \n", "\n", "           country original_currency  amount_eur  \n", "864   Burkina Faso               XOF     1423.92  \n", "865   Burkina Faso               XOF     3235.52  \n", "866   Burkina Faso               XOF     2888.10  \n", "867   Burkina Faso               XOF     2860.13  \n", "868   Burkina Faso               XOF     2684.18  \n", "...            ...               ...         ...  \n", "1291  Burkina Faso               XOF     3994.75  \n", "1292  Burkina Faso               XOF     1757.12  \n", "1293  Burkina Faso               XOF     2288.36  \n", "1294  Burkina Faso               XOF     1364.32  \n", "1295  Burkina Faso               XOF     2269.23  \n", "\n", "[432 rows x 8 columns]"]}, "execution_count": 129, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df [~ expenses_df['amount_eur'].isna()]"]}, {"cell_type": "code", "execution_count": 130, "id": "51163e01", "metadata": {}, "outputs": [{"data": {"text/plain": ["432"]}, "execution_count": 130, "metadata": {}, "output_type": "execute_result"}], "source": ["len(expenses_df [~ expenses_df['amount_eur'].isna()])"]}, {"cell_type": "code", "execution_count": 131, "id": "8e0afc84", "metadata": {}, "outputs": [{"data": {"text/plain": ["date                    0\n", "department              0\n", "category                0\n", "amount_local          576\n", "project_id              0\n", "country                 0\n", "original_currency       0\n", "amount_eur           3024\n", "dtype: int64"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.isna().sum()"]}, {"cell_type": "code", "execution_count": 132, "id": "f3160d4a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>362</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>2025-07-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>2025-07-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>576 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           date department             category  amount_local project_id  \\\n", "360  2025-07-31         HR             Salaries           NaN       BE55   \n", "361  2025-07-31         HR             Training           NaN       BE55   \n", "362  2025-07-31         HR          Recruitment           NaN       BE55   \n", "363  2025-07-31    Medical          Medications           NaN       BE55   \n", "364  2025-07-31    Medical    Medical Equipment           NaN       BE55   \n", "...         ...        ...                  ...           ...        ...   \n", "3451 2025-12-31  Logistics            Transport           NaN       BF01   \n", "3452 2025-12-31  Logistics  Vehicle Maintenance           NaN       BF01   \n", "3453 2025-12-31     Supply             Supplies           NaN       BF01   \n", "3454 2025-12-31     Supply          Warehousing           NaN       BF01   \n", "3455 2025-12-31     Supply           Cold Chain           NaN       BF01   \n", "\n", "           country original_currency  amount_eur  \n", "360        Belgium               EUR         NaN  \n", "361        Belgium               EUR         NaN  \n", "362        Belgium               EUR         NaN  \n", "363        Belgium               EUR         NaN  \n", "364        Belgium               EUR         NaN  \n", "...            ...               ...         ...  \n", "3451  Burkina Faso               XOF         NaN  \n", "3452  Burkina Faso               XOF         NaN  \n", "3453  Burkina Faso               XOF         NaN  \n", "3454  Burkina Faso               XOF         NaN  \n", "3455  Burkina Faso               XOF         NaN  \n", "\n", "[576 rows x 8 columns]"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["# check the rows in which amount_local is NaN\n", "expenses_df[expenses_df['amount_local'].isna()]"]}, {"cell_type": "code", "execution_count": 133, "id": "9d23f987", "metadata": {}, "outputs": [{"data": {"text/plain": ["576"]}, "execution_count": 133, "metadata": {}, "output_type": "execute_result"}], "source": ["len(expenses_df[expenses_df['amount_local'].isna()])"]}, {"cell_type": "code", "execution_count": null, "id": "8fb57ed9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f111cc49", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a273ea3d", "metadata": {}, "source": ["## silver layer budget df"]}, {"cell_type": "code", "execution_count": 136, "id": "46a605d1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year  month department           category  budget_eur version  \\\n", "0   1  2023      1         HR           Salaries     2293.00      v1   \n", "1   2  2023      1         HR           Training     4768.76      v1   \n", "2   3  2023      1         HR        Recruitment     1642.21      v1   \n", "3   4  2023      1    Medical        Medications     1377.75      v1   \n", "4   5  2023      1    Medical  Medical Equipment     2062.46      v1   \n", "\n", "  project_id  country  \n", "0       BE55  Belgium  \n", "1       BE55  Belgium  \n", "2       BE55  Belgium  \n", "3       BE55  Belgium  \n", "4       BE55  Belgium  "]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["# read the bronze budget data\n", "budget_df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/bronze_budget_data.parquet')\n", "budget_df.head()"]}, {"cell_type": "code", "execution_count": 137, "id": "a4469bc7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 6 columns):\n", " #   Column      Non-Null Count  Dtype         \n", "---  ------      --------------  -----         \n", " 0   department  3456 non-null   object        \n", " 1   category    3456 non-null   object        \n", " 2   budget_eur  3456 non-null   float64       \n", " 3   project_id  3456 non-null   object        \n", " 4   country     3456 non-null   object        \n", " 5   date        3456 non-null   datetime64[ns]\n", "dtypes: datetime64[ns](1), float64(1), object(4)\n", "memory usage: 162.1+ KB\n"]}], "source": ["# drop version column, id column\n", "budget_df.drop(columns=['version', 'id'], inplace=True)\n", "\n", "# convert the 'year' and 'month' columns to string format\n", "budget_df['year'] = budget_df['year'].astype(str)\n", "budget_df['month'] = budget_df['month'].astype(str)\n", "\n", "# create a temporary date string with the 1st day of the month\n", "budget_df['temp_date_str'] = budget_df['year'] + '-' + budget_df['month'].str.zfill(2) + '-01'\n", "\n", "# convert the temporary date string to datetime objects\n", "budget_df['date'] = pd.to_datetime(budget_df['temp_date_str'], errors='coerce')\n", "\n", "# convert to the last day of the month using MonthEnd\n", "budget_df['date'] = budget_df['date'] + MonthEnd(1)\n", "\n", "# drop the intermediate columns if no longer needed\n", "budget_df = budget_df.drop(columns=['year', 'month', 'temp_date_str'])\n", "\n", "# display updated info and head to verify\n", "budget_df.info()"]}, {"cell_type": "code", "execution_count": 139, "id": "19ea529f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  budget_eur project_id  country\n", "0 2023-01-31         HR           Salaries     2293.00       BE55  Belgium\n", "1 2023-01-31         HR           Training     4768.76       BE55  Belgium\n", "2 2023-01-31         HR        Recruitment     1642.21       BE55  Belgium\n", "3 2023-01-31    Medical        Medications     1377.75       BE55  Belgium\n", "4 2023-01-31    Medical  Medical Equipment     2062.46       BE55  Belgium"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["# move date column to the first position\n", "budget_df = budget_df[['date'] + [col for col in budget_df.columns if col != 'date']]\n", "\n", "budget_df.head()"]}, {"cell_type": "code", "execution_count": 142, "id": "e56cf215", "metadata": {}, "outputs": [], "source": ["# save the budget dataframe to silver parquet\n", "budget_df.to_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_budget_data.parquet', index=False)\n"]}, {"cell_type": "code", "execution_count": 143, "id": "15080e11", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  budget_eur project_id  country\n", "0 2023-01-31         HR           Salaries     2293.00       BE55  Belgium\n", "1 2023-01-31         HR           Training     4768.76       BE55  Belgium\n", "2 2023-01-31         HR        Recruitment     1642.21       BE55  Belgium\n", "3 2023-01-31    Medical        Medications     1377.75       BE55  Belgium\n", "4 2023-01-31    Medical  Medical Equipment     2062.46       BE55  Belgium"]}, "execution_count": 143, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_budget_data.parquet')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 144, "id": "693c3efa", "metadata": {}, "outputs": [{"data": {"text/plain": ["date          0\n", "department    0\n", "category      0\n", "budget_eur    0\n", "project_id    0\n", "country       0\n", "dtype: int64"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 145, "id": "c6e608c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 6 columns):\n", " #   Column      Non-Null Count  Dtype         \n", "---  ------      --------------  -----         \n", " 0   date        3456 non-null   datetime64[ns]\n", " 1   department  3456 non-null   object        \n", " 2   category    3456 non-null   object        \n", " 3   budget_eur  3456 non-null   float64       \n", " 4   project_id  3456 non-null   object        \n", " 5   country     3456 non-null   object        \n", "dtypes: datetime64[ns](1), float64(1), object(4)\n", "memory usage: 162.1+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 146, "id": "68a58f1c", "metadata": {}, "outputs": [{"data": {"text/plain": ["3456"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df)"]}, {"cell_type": "code", "execution_count": null, "id": "d56c0055", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 147, "id": "142bb71e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur  \n", "0               EUR         NaN  \n", "1               EUR         NaN  \n", "2               EUR         NaN  \n", "3               EUR         NaN  \n", "4               EUR         NaN  "]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.head()"]}, {"cell_type": "code", "execution_count": 148, "id": "f9b588a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["date                    0\n", "department              0\n", "category                0\n", "amount_local          576\n", "project_id              0\n", "country                 0\n", "original_currency       0\n", "amount_eur           3024\n", "dtype: int64"]}, "execution_count": 148, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 149, "id": "cd63435e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>362</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>2025-07-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>2025-07-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>576 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           date department             category  amount_local project_id  \\\n", "360  2025-07-31         HR             Salaries           NaN       BE55   \n", "361  2025-07-31         HR             Training           NaN       BE55   \n", "362  2025-07-31         HR          Recruitment           NaN       BE55   \n", "363  2025-07-31    Medical          Medications           NaN       BE55   \n", "364  2025-07-31    Medical    Medical Equipment           NaN       BE55   \n", "...         ...        ...                  ...           ...        ...   \n", "3451 2025-12-31  Logistics            Transport           NaN       BF01   \n", "3452 2025-12-31  Logistics  Vehicle Maintenance           NaN       BF01   \n", "3453 2025-12-31     Supply             Supplies           NaN       BF01   \n", "3454 2025-12-31     Supply          Warehousing           NaN       BF01   \n", "3455 2025-12-31     Supply           Cold Chain           NaN       BF01   \n", "\n", "           country original_currency  amount_eur  \n", "360        Belgium               EUR         NaN  \n", "361        Belgium               EUR         NaN  \n", "362        Belgium               EUR         NaN  \n", "363        Belgium               EUR         NaN  \n", "364        Belgium               EUR         NaN  \n", "...            ...               ...         ...  \n", "3451  Burkina Faso               XOF         NaN  \n", "3452  Burkina Faso               XOF         NaN  \n", "3453  Burkina Faso               XOF         NaN  \n", "3454  Burkina Faso               XOF         NaN  \n", "3455  Burkina Faso               XOF         NaN  \n", "\n", "[576 rows x 8 columns]"]}, "execution_count": 149, "metadata": {}, "output_type": "execute_result"}], "source": ["# display rows where amount_local is null\n", "expenses_df[expenses_df['amount_local'].isnull()]"]}, {"cell_type": "code", "execution_count": 151, "id": "1baab5aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["576"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["len(expenses_df[expenses_df['amount_local'].isnull()])"]}, {"cell_type": "code", "execution_count": 154, "id": "c879ddbb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>361</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>362</th>\n", "      <td>2025-07-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>2025-07-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>2025-07-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>NaN</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date department           category  amount_local project_id  \\\n", "360 2025-07-31         HR           Salaries           NaN       BE55   \n", "361 2025-07-31         HR           Training           NaN       BE55   \n", "362 2025-07-31         HR        Recruitment           NaN       BE55   \n", "363 2025-07-31    Medical        Medications           NaN       BE55   \n", "364 2025-07-31    Medical  Medical Equipment           NaN       BE55   \n", "\n", "     country original_currency  amount_eur  \n", "360  Belgium               EUR         NaN  \n", "361  Belgium               EUR         NaN  \n", "362  Belgium               EUR         NaN  \n", "363  Belgium               EUR         NaN  \n", "364  Belgium               EUR         NaN  "]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["# find there are how many rows that have dates from 2025-07-01 to 2025-12-31\n", "mask = (expenses_df['date'] >= '2025-07-01') & (expenses_df['date'] <= '2025-12-31')\n", "expenses_df[mask].head()"]}, {"cell_type": "code", "execution_count": 156, "id": "69dcf5e1", "metadata": {}, "outputs": [], "source": ["len(expenses_df[mask])\n", "\n", "# ave the expenses dataframe to silver parquet\n", "expenses_df.to_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet', index=False)"]}, {"cell_type": "code", "execution_count": 159, "id": "48a4d495", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur  \n", "0               EUR         NaN  \n", "1               EUR         NaN  \n", "2               EUR         NaN  \n", "3               EUR         NaN  \n", "4               EUR         NaN  "]}, "execution_count": 159, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 160, "id": "b2fb710b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur  \n", "0               EUR         NaN  \n", "1               EUR         NaN  \n", "2               EUR         NaN  \n", "3               EUR         NaN  \n", "4               EUR         NaN  "]}, "execution_count": 160, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.head()"]}, {"cell_type": "code", "execution_count": 161, "id": "57607e78", "metadata": {}, "outputs": [], "source": ["# One thing remaining for our expenses_df is amount_eur column. We need to convert amount_local to amount_eur based on the exchange rate.\n"]}, {"cell_type": "code", "execution_count": 163, "id": "2e1d0efd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Silver Layer: Starting Currency Conversion for Expenses ---\n", "  Fetched KES to EUR rate: 0.00657\n", "  Fetched XOF to EUR rate: 0.001524\n", "  Set EUR to EUR rate: 1.0\n"]}, {"ename": "KeyError", "evalue": "'original_currency_map'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/indexes/base.py:3812\u001b[39m, in \u001b[36mIndex.get_loc\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   3811\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3812\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_engine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3813\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/index.pyx:167\u001b[39m, in \u001b[36mpandas._libs.index.IndexEngine.get_loc\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/index.pyx:196\u001b[39m, in \u001b[36mpandas._libs.index.IndexEngine.get_loc\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/hashtable_class_helper.pxi:7088\u001b[39m, in \u001b[36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/hashtable_class_helper.pxi:7096\u001b[39m, in \u001b[36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mKeyError\u001b[39m: 'original_currency_map'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[163]\u001b[39m\u001b[32m, line 80\u001b[39m\n\u001b[32m     77\u001b[39m expenses_df[\u001b[33m'\u001b[39m\u001b[33mamount_local\u001b[39m\u001b[33m'\u001b[39m] = pd.to_numeric(expenses_df[\u001b[33m'\u001b[39m\u001b[33mamount_local\u001b[39m\u001b[33m'\u001b[39m], errors=\u001b[33m'\u001b[39m\u001b[33mcoerce\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     79\u001b[39m \u001b[38;5;66;03m# Apply the conversion function row-wise\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m80\u001b[39m expenses_df[\u001b[33m'\u001b[39m\u001b[33mamount_eur\u001b[39m\u001b[33m'\u001b[39m] = \u001b[43mexpenses_df\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconvert_amount_to_eur\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     82\u001b[39m \u001b[38;5;66;03m# Fill any remaining NaN in amount_eur (e.g., from conversion failures or original NaNs) with 0\u001b[39;00m\n\u001b[32m     83\u001b[39m expenses_df[\u001b[33m'\u001b[39m\u001b[33mamount_eur\u001b[39m\u001b[33m'\u001b[39m] = expenses_df[\u001b[33m'\u001b[39m\u001b[33mamount_eur\u001b[39m\u001b[33m'\u001b[39m].fillna(\u001b[32m0\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/frame.py:10381\u001b[39m, in \u001b[36mDataFrame.apply\u001b[39m\u001b[34m(self, func, axis, raw, result_type, args, by_row, engine, engine_kwargs, **kwargs)\u001b[39m\n\u001b[32m  10367\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapply\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m frame_apply\n\u001b[32m  10369\u001b[39m op = frame_apply(\n\u001b[32m  10370\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m  10371\u001b[39m     func=func,\n\u001b[32m   (...)\u001b[39m\u001b[32m  10379\u001b[39m     kwargs=kwargs,\n\u001b[32m  10380\u001b[39m )\n\u001b[32m> \u001b[39m\u001b[32m10381\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m.__finalize__(\u001b[38;5;28mself\u001b[39m, method=\u001b[33m\"\u001b[39m\u001b[33mapply\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/apply.py:916\u001b[39m, in \u001b[36mFrameApply.apply\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    913\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m.raw:\n\u001b[32m    914\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.apply_raw(engine=\u001b[38;5;28mself\u001b[39m.engine, engine_kwargs=\u001b[38;5;28mself\u001b[39m.engine_kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m916\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapply_standard\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/apply.py:1063\u001b[39m, in \u001b[36mFrameApply.apply_standard\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1061\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mapply_standard\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m   1062\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.engine == \u001b[33m\"\u001b[39m\u001b[33mpython\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m1063\u001b[39m         results, res_index = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapply_series_generator\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1064\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1065\u001b[39m         results, res_index = \u001b[38;5;28mself\u001b[39m.apply_series_numba()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/apply.py:1081\u001b[39m, in \u001b[36mFrameApply.apply_series_generator\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1078\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m option_context(\u001b[33m\"\u001b[39m\u001b[33mmode.chained_assignment\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m   1079\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m i, v \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(series_gen):\n\u001b[32m   1080\u001b[39m         \u001b[38;5;66;03m# ignore SettingWithCopy here in case the user mutates\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1081\u001b[39m         results[i] = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1082\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(results[i], ABCSeries):\n\u001b[32m   1083\u001b[39m             \u001b[38;5;66;03m# If we have a view on v, we need to make a copy because\u001b[39;00m\n\u001b[32m   1084\u001b[39m             \u001b[38;5;66;03m#  series_generator will swap out the underlying data\u001b[39;00m\n\u001b[32m   1085\u001b[39m             results[i] = results[i].copy(deep=\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[163]\u001b[39m\u001b[32m, line 49\u001b[39m, in \u001b[36mconvert_amount_to_eur\u001b[39m\u001b[34m(row)\u001b[39m\n\u001b[32m     47\u001b[39m original_amount = row[\u001b[33m'\u001b[39m\u001b[33mamount_local\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m     48\u001b[39m \u001b[38;5;66;03m# Use 'original_currency_map' as the source of truth for currency\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m49\u001b[39m original_currency = \u001b[43mrow\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43moriginal_currency_map\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m \n\u001b[32m     51\u001b[39m \u001b[38;5;66;03m# Handle cases where original_amount might be NaN or not numeric\u001b[39;00m\n\u001b[32m     52\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m pd.isna(original_amount):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/series.py:1130\u001b[39m, in \u001b[36mSeries.__getitem__\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   1127\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._values[key]\n\u001b[32m   1129\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m key_is_scalar:\n\u001b[32m-> \u001b[39m\u001b[32m1130\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_value\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1132\u001b[39m \u001b[38;5;66;03m# Convert generator to list before going through hashable part\u001b[39;00m\n\u001b[32m   1133\u001b[39m \u001b[38;5;66;03m# (We will iterate through the generator there to check for slices)\u001b[39;00m\n\u001b[32m   1134\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/series.py:1246\u001b[39m, in \u001b[36mSeries._get_value\u001b[39m\u001b[34m(self, label, takeable)\u001b[39m\n\u001b[32m   1243\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._values[label]\n\u001b[32m   1245\u001b[39m \u001b[38;5;66;03m# Similar to Index.get_value, but we do not fall back to positional\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1246\u001b[39m loc = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mindex\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlabel\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1248\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m is_integer(loc):\n\u001b[32m   1249\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._values[loc]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/lib/python3.13/site-packages/pandas/core/indexes/base.py:3819\u001b[39m, in \u001b[36mIndex.get_loc\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   3814\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[32m   3815\u001b[39m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc.Iterable)\n\u001b[32m   3816\u001b[39m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[32m   3817\u001b[39m     ):\n\u001b[32m   3818\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[32m-> \u001b[39m\u001b[32m3819\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m   3820\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[32m   3821\u001b[39m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[32m   3822\u001b[39m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[32m   3823\u001b[39m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[32m   3824\u001b[39m     \u001b[38;5;28mself\u001b[39m._check_indexing_error(key)\n", "\u001b[31mKeyError\u001b[39m: 'original_currency_map'"]}], "source": ["# --- Silver Layer: Currency Conversion for Expenses DataFrame ---\n", "\n", "import pandas as pd\n", "# Import the get_latest_exchange_rate function from a separate utility file (e.g., api_utils.py)\n", "# This assumes you have a file named api_utils.py in your project structure\n", "# and that it correctly imports API_KEY and BASE_URL from config.py.\n", "from get_latest_exchange_rate import get_latest_exchange_rate \n", "\n", "# Import PROJECT_CURRENCY_MAP from your central config file\n", "# from config import PROJECT_CURRENCY_MAP\n", "\n", "# Assuming expenses_df is already loaded from bronze_expenses_data.parquet\n", "\n", "print(\"\\n--- <PERSON> Layer: Starting Currency Conversion for Expenses ---\")\n", "\n", "# Step 1: Fetch all necessary exchange rates and store them in a dictionary\n", "# We need to convert KES to EUR and XOF to EUR.\n", "# Also, we might need EUR to EUR (rate 1.0) for consistency.\n", "\n", "# Initialize a dictionary to hold our rates (this cache is specific to this run,\n", "# the one in api_utils.py handles caching across calls to that function)\n", "currency_conversion_rates = {}\n", "\n", "# Fetch KES to EUR\n", "kes_to_eur_rate = get_latest_exchange_rate(\"KES\", \"EUR\")\n", "if kes_to_eur_rate is not None:\n", "    currency_conversion_rates['KES_to_EUR'] = kes_to_eur_rate\n", "    print(f\"  Fetched KES to EUR rate: {kes_to_eur_rate}\")\n", "else:\n", "    print(\"  Warning: Failed to fetch KES to EUR rate. KES amounts will not be converted.\")\n", "\n", "# Fetch XOF to EUR\n", "xof_to_eur_rate = get_latest_exchange_rate(\"XOF\", \"EUR\")\n", "if xof_to_eur_rate is not None:\n", "    currency_conversion_rates['XOF_to_EUR'] = xof_to_eur_rate\n", "    print(f\"  Fetched XOF to EUR rate: {xof_to_eur_rate}\")\n", "else:\n", "    print(\"  Warning: Failed to fetch XOF to EUR rate. XOF amounts will not be converted.\")\n", "\n", "# EUR to EUR rate (always 1.0) for consistency in logic\n", "currency_conversion_rates['EUR_to_EUR'] = 1.0\n", "print(f\"  Set EUR to EUR rate: {currency_conversion_rates['EUR_to_EUR']}\")\n", "\n", "\n", "# Step 2: Define a function to apply the conversion to each row\n", "def convert_amount_to_eur(row):\n", "    original_amount = row['amount_local']\n", "    # Use 'original_currency_map' as the source of truth for currency\n", "    original_currency = row['original_currency_map'] \n", "    \n", "    # Handle cases where original_amount might be NaN or not numeric\n", "    if pd.isna(original_amount):\n", "        return None # Or 0, depending on how you want to treat missing amounts\n", "\n", "    if original_currency == 'EUR':\n", "        return original_amount # Already in EUR\n", "    elif original_currency == 'KES':\n", "        rate = currency_conversion_rates.get('KES_to_EUR')\n", "        if rate is not None:\n", "            return original_amount * rate\n", "        else:\n", "            print(f\"  Warning: Missing KES to EUR rate for Project {row['project_id']}, Date {row['date']}. Amount not converted.\")\n", "            return None # Or original_amount if you want to keep it in local currency\n", "    elif original_currency == 'XOF':\n", "        rate = currency_conversion_rates.get('XOF_to_EUR')\n", "        if rate is not None:\n", "            return original_amount * rate\n", "        else:\n", "            print(f\"  Warning: Missing XOF to EUR rate for Project {row['project_id']}, Date {row['date']}. Amount not converted.\")\n", "            return None # Or original_amount\n", "    else:\n", "        print(f\"  Warning: Unrecognized currency '{original_currency}' for Project {row['project_id']}, Date {row['date']}. Amount not converted.\")\n", "        return None # Or original_amount\n", "\n", "# Step 3: Apply the conversion function to create/update 'amount_eur' column\n", "# Ensure 'amount_local' is numeric before applying\n", "expenses_df['amount_local'] = pd.to_numeric(expenses_df['amount_local'], errors='coerce')\n", "\n", "# Apply the conversion function row-wise\n", "expenses_df['amount_eur'] = expenses_df.apply(convert_amount_to_eur, axis=1)\n", "\n", "# Fill any remaining NaN in amount_eur (e.g., from conversion failures or original NaNs) with 0\n", "expenses_df['amount_eur'] = expenses_df['amount_eur'].fillna(0)\n", "\n", "print(\"  'amount_eur' column updated with converted values.\")\n", "\n", "# --- Step 4: Drop the original 'original_currency' from DB if not needed ---\n", "# Assuming 'original_currency' is the DB's currency column that might be incorrect.\n", "# We are using 'original_currency_map' as the source of truth for currency.\n", "if 'original_currency' in expenses_df.columns:\n", "    expenses_df = expenses_df.drop(columns=['original_currency'])\n", "    print(\"  Dropped 'original_currency' column (from DB).\")\n", "\n", "# Also, you might want to drop the 'amount_local' if 'amount_eur' is the final currency needed.\n", "# However, keeping it might be useful for auditing or if local currency reporting is ever needed.\n", "# For this test, let's keep it for now unless explicitly told to remove.\n", "\n", "# --- Step 5: Handle the date column ---\n", "# Your head() output shows the first column is a date, but it's unnamed.\n", "# Let's assume its name is 'date' or similar from the DB.\n", "# You MUST replace 'date_column_name_from_db' with the actual column name from your all_expenses_df.info()\n", "# If your first column is truly unnamed, you might need to rename it first.\n", "# For now, I'll assume the date column is named 'date' based on common patterns.\n", "# If it's unnamed, you might need to do: expenses_df.rename(columns={expenses_df.columns[0]: 'date'}, inplace=True)\n", "# Then proceed with conversion.\n", "\n", "# Convert the date column to datetime objects\n", "# Replace 'date' with the actual column name if different\n", "expenses_df['date'] = pd.to_datetime(expenses_df['date'], errors='coerce')\n", "print(\"  Date column converted to datetime.\")\n", "\n", "# Drop rows where date conversion failed (NaT) or amount_eur is 0 (if that's desired for cleanup)\n", "expenses_df = expenses_df.dropna(subset=['date', 'amount_eur'])\n", "print(f\"  Dropped rows with missing essential 'date' or 'amount_eur'. Remaining rows: {len(expenses_df)}\")\n", "\n", "\n", "# Display updated info and head to verify\n", "print(\"\\n--- Updated Expenses DataFrame Info (after currency conversion) ---\")\n", "expenses_df.info()\n", "print(\"\\n--- Updated Expenses DataFrame Head (after currency conversion) ---\")\n", "print(expenses_df.head())"]}, {"cell_type": "markdown", "id": "777e269c", "metadata": {}, "source": ["# ROUGH WORK\n", "## conversion of amount_local to amount_eur"]}, {"cell_type": "code", "execution_count": 165, "id": "29a6e158", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 8 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   date               3456 non-null   datetime64[ns]\n", " 1   department         3456 non-null   object        \n", " 2   category           3456 non-null   object        \n", " 3   amount_local       2880 non-null   float64       \n", " 4   project_id         3456 non-null   object        \n", " 5   country            3456 non-null   object        \n", " 6   original_currency  3456 non-null   object        \n", " 7   amount_eur         432 non-null    float64       \n", "dtypes: datetime64[ns](1), float64(2), object(5)\n", "memory usage: 216.1+ KB\n", "date                    0\n", "department              0\n", "category                0\n", "amount_local          576\n", "project_id              0\n", "country                 0\n", "original_currency       0\n", "amount_eur           3024\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  amount_eur  \n", "0               EUR         NaN  \n", "1               EUR         NaN  \n", "2               EUR         NaN  \n", "3               EUR         NaN  \n", "4               EUR         NaN  "]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet')\n", "df.info()\n", "print(df.isnull().sum())\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0f913220", "metadata": {}, "outputs": [], "source": ["# define a function to convert amount_local to amount_eur based on the exchange rate\n", "def convert_amount_to_eur(row):\n", "    original_amount = row['amount_local']\n", "    original_currency = row['original_currency_map']\n", "    if pd.isna(original_amount):\n", "        return None\n", "    if original_currency == 'EUR':\n", "        return original_amount\n", "    elif original_currency == 'KES':\n", "        rate = get_latest_exchange_rate(\"KES\", \"EUR\")\n", "        if rate is not None:\n", "            return original_amount * rate\n", "        else:"]}, {"cell_type": "code", "execution_count": null, "id": "72baec19", "metadata": {}, "outputs": [], "source": ["# when amount local is not NaN, convert amount_local to amount_eur based on the exchange rate\n", "# and update the amoount_eur column\n", "expenses_df['amount_eur'] = expenses_df.apply(convert_amount_to_eur, axis=1)\n", "expenses_df['amount_eur'] = expenses_df['amount_eur'].fillna(0)\n", "expenses_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4205567b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 166, "id": "c287fbbe", "metadata": {}, "outputs": [], "source": ["# RESTART"]}, {"cell_type": "code", "execution_count": 167, "id": "5d2d0840", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["date                    0\n", "department              0\n", "category                0\n", "amount_local          576\n", "project_id              0\n", "country                 0\n", "original_currency       0\n", "amount_eur           3024\n", "dtype: int64\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 8 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   date               3456 non-null   datetime64[ns]\n", " 1   department         3456 non-null   object        \n", " 2   category           3456 non-null   object        \n", " 3   amount_local       2880 non-null   float64       \n", " 4   project_id         3456 non-null   object        \n", " 5   country            3456 non-null   object        \n", " 6   original_currency  3456 non-null   object        \n", " 7   amount_eur         432 non-null    float64       \n", "dtypes: datetime64[ns](1), float64(2), object(5)\n", "memory usage: 216.1+ KB\n"]}], "source": ["import pandas as pd\n", "import sys\n", "import os\n", "import numpy as np # Often useful for conditional operations\n", "\n", "# Add the directory containing get_latest_exchange_rate.py to the Python path\n", "# This assumes get_latest_exchange_rate.py is in the same directory as the parquet file.\n", "# script_dir = os.path.dirname('/Users/<USER>/test/MSF-test/processed_data/')\n", "# if script_dir not in sys.path:\n", "#     sys.path.insert(0, script_dir)\n", "\n", "from get_latest_exchange_rate import get_latest_exchange_rate\n", "\n", "# --- Load the DataFrame ---\n", "df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet')\n", "df.head()\n", "print(df.isnull().sum())\n", "df.info()"]}, {"cell_type": "code", "execution_count": 168, "id": "42c006fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Fetching exchange rates...\n"]}], "source": ["\n", "# Display initial info\n", "# print(\"Initial DataFrame Info:\")\n", "# df.info()\n", "# print(\"\\nInitial Null Counts:\")\n", "# print(df.isnull().sum())\n", "# print(\"\\nInitial DataFrame Head:\")\n", "# print(df.head())\n", "\n", "# --- Step 1: Fetch the required exchange rates once ---\n", "\n", "\n", "# Define rates\n", "print(\"\\nFetching exchange rates...\")\n", "eur_to_eur_rate = 1.0\n", "kes_to_eur_rate = get_latest_exchange_rate(\"KES\", \"EUR\")\n", "xof_to_eur_rate = get_latest_exchange_rate(\"XOF\", \"EUR\")\n", "\n", "\n", "# --- Step 2: Create a rate mapping dictionary ---\n", "rate_mapping = {\n", "    'EUR': eur_to_eur_rate,\n", "    'KES': kes_to_eur_rate,\n", "    'XOF': xof_to_eur_rate\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 169, "id": "34b5fee1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "'amount_eur' column dropped (if it existed).\n"]}], "source": ["\n", "# --- Step 3: Drop the existing 'amount_eur' column if it exists ---\n", "if 'amount_eur' in df.columns:\n", "    df = df.drop(columns=['amount_eur'])\n", "    print(\"\\n'amount_eur' column dropped (if it existed).\")\n", "\n", "# --- Step 4: Create the 'rate' column based on 'original_currency' ---\n", "# Using .map() is efficient for this kind of lookup\n", "df['rate'] = df['original_currency'].map(rate_mapping)\n"]}, {"cell_type": "code", "execution_count": 171, "id": "20cd2965", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3456 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           date department             category  amount_local project_id  \\\n", "0    2023-01-31         HR             Salaries       2278.71       BE55   \n", "1    2023-01-31         HR             Training       4053.43       BE55   \n", "2    2023-01-31         HR          Recruitment       1450.10       BE55   \n", "3    2023-01-31    Medical          Medications       1275.95       BE55   \n", "4    2023-01-31    Medical    Medical Equipment       2182.81       BE55   \n", "...         ...        ...                  ...           ...        ...   \n", "3451 2025-12-31  Logistics            Transport           NaN       BF01   \n", "3452 2025-12-31  Logistics  Vehicle Maintenance           NaN       BF01   \n", "3453 2025-12-31     Supply             Supplies           NaN       BF01   \n", "3454 2025-12-31     Supply          Warehousing           NaN       BF01   \n", "3455 2025-12-31     Supply           Cold Chain           NaN       BF01   \n", "\n", "           country original_currency      rate  \n", "0          Belgium               EUR  1.000000  \n", "1          Belgium               EUR  1.000000  \n", "2          Belgium               EUR  1.000000  \n", "3          Belgium               EUR  1.000000  \n", "4          Belgium               EUR  1.000000  \n", "...            ...               ...       ...  \n", "3451  Burkina Faso               XOF  0.001524  \n", "3452  Burkina Faso               XOF  0.001524  \n", "3453  Burkina Faso               XOF  0.001524  \n", "3454  Burkina Faso               XOF  0.001524  \n", "3455  Burkina Faso               XOF  0.001524  \n", "\n", "[3456 rows x 8 columns]"]}, "execution_count": 171, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 172, "id": "4cd7b590", "metadata": {}, "outputs": [], "source": ["\n", "# # Handle cases where original_currency might not be in our predefined map (optional, but good for robustness)\n", "# # If there are other currencies, their 'rate' will be NaN.\n", "# # You could add a check here or decide on a default action (e.g., set to 0 or raise error).\n", "# unmapped_currencies = df[df['rate'].isna() & df['original_currency'].notna()]['original_currency'].unique()\n", "# if len(unmapped_currencies) > 0:\n", "#     print(f\"\\nWarning: The following currencies were found but no rate was defined for them: {unmapped_currencies}\")\n", "#     print(\"Their 'rate' and consequently 'amount_eur' will be NaN.\")\n", "\n", "# print(\"\\n'rate' column created.\")\n", "\n", "# --- Step 5: Calculate 'amount_eur' ---\n", "# Multiplying by amount_local will correctly propagate NaNs from amount_local\n", "df['amount_eur'] = df['amount_local'] * df['rate']\n", "\n", "# print(\"\\n'amount_eur' column calculated.\")\n", "\n", "# # --- Step 6: Display updated DataFrame info and head ---\n", "# print(\"\\nUpdated DataFrame Info:\")\n", "# df.info()\n", "# print(\"\\nUpdated Null Counts:\")\n", "# print(df.isnull().sum())\n", "# print(\"\\nUpdated DataFrame Head with 'rate' and 'amount_eur':\")\n", "# print(df.head())\n", "\n", "# # To verify nulls for 2025-07-31 to 2025-12-31\n", "# print(\"\\nChecking nulls for dates 2025-07-31 to 2025-12-31:\")\n", "# future_dates_df = df[(df['date'] >= '2025-07-31') & (df['date'] <= '2025-12-31')]\n", "# print(future_dates_df[['date', 'amount_local', 'rate', 'amount_eur']].isnull().sum())\n", "# print(future_dates_df[['date', 'amount_local', 'rate', 'amount_eur']].head())\n", "\n", "# # Optional: Display some rows where conversion happened\n", "# print(\"\\nSample rows with 'amount_eur' calculated (first 10 non-null values):\")\n", "# print(df[df['amount_eur'].notna()].head(10))"]}, {"cell_type": "code", "execution_count": 173, "id": "4adbf1b3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>rate</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "      <td>2278.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "      <td>4053.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "      <td>1450.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "      <td>1275.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.000000</td>\n", "      <td>2182.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3451</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Transport</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3452</th>\n", "      <td>2025-12-31</td>\n", "      <td>Logistics</td>\n", "      <td>Vehicle Maintenance</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3453</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Supplies</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3454</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Warehousing</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3455</th>\n", "      <td>2025-12-31</td>\n", "      <td>Supply</td>\n", "      <td>Cold Chain</td>\n", "      <td>NaN</td>\n", "      <td>BF01</td>\n", "      <td>Burkina Faso</td>\n", "      <td>XOF</td>\n", "      <td>0.001524</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3456 rows × 9 columns</p>\n", "</div>"], "text/plain": ["           date department             category  amount_local project_id  \\\n", "0    2023-01-31         HR             Salaries       2278.71       BE55   \n", "1    2023-01-31         HR             Training       4053.43       BE55   \n", "2    2023-01-31         HR          Recruitment       1450.10       BE55   \n", "3    2023-01-31    Medical          Medications       1275.95       BE55   \n", "4    2023-01-31    Medical    Medical Equipment       2182.81       BE55   \n", "...         ...        ...                  ...           ...        ...   \n", "3451 2025-12-31  Logistics            Transport           NaN       BF01   \n", "3452 2025-12-31  Logistics  Vehicle Maintenance           NaN       BF01   \n", "3453 2025-12-31     Supply             Supplies           NaN       BF01   \n", "3454 2025-12-31     Supply          Warehousing           NaN       BF01   \n", "3455 2025-12-31     Supply           Cold Chain           NaN       BF01   \n", "\n", "           country original_currency      rate  amount_eur  \n", "0          Belgium               EUR  1.000000     2278.71  \n", "1          Belgium               EUR  1.000000     4053.43  \n", "2          Belgium               EUR  1.000000     1450.10  \n", "3          Belgium               EUR  1.000000     1275.95  \n", "4          Belgium               EUR  1.000000     2182.81  \n", "...            ...               ...       ...         ...  \n", "3451  Burkina Faso               XOF  0.001524         NaN  \n", "3452  Burkina Faso               XOF  0.001524         NaN  \n", "3453  Burkina Faso               XOF  0.001524         NaN  \n", "3454  Burkina Faso               XOF  0.001524         NaN  \n", "3455  Burkina Faso               XOF  0.001524         NaN  \n", "\n", "[3456 rows x 9 columns]"]}, "execution_count": 173, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 175, "id": "358c1cbd", "metadata": {}, "outputs": [{"data": {"text/plain": ["date                   0\n", "department             0\n", "category               0\n", "amount_local         576\n", "project_id             0\n", "country                0\n", "original_currency      0\n", "rate                   0\n", "amount_eur           576\n", "dtype: int64"]}, "execution_count": 175, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 177, "id": "f98385f0", "metadata": {}, "outputs": [], "source": ["# save the dataframe to a parquet file\n", "df.to_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet')"]}, {"cell_type": "code", "execution_count": null, "id": "4eb5dbd0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 178, "id": "2ec77da3", "metadata": {}, "outputs": [], "source": ["# GOLD LAYER"]}, {"cell_type": "code", "execution_count": 180, "id": "489d25ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["date          0\n", "department    0\n", "category      0\n", "budget_eur    0\n", "project_id    0\n", "country       0\n", "dtype: int64\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 6 columns):\n", " #   Column      Non-Null Count  Dtype         \n", "---  ------      --------------  -----         \n", " 0   date        3456 non-null   datetime64[ns]\n", " 1   department  3456 non-null   object        \n", " 2   category    3456 non-null   object        \n", " 3   budget_eur  3456 non-null   float64       \n", " 4   project_id  3456 non-null   object        \n", " 5   country     3456 non-null   object        \n", "dtypes: datetime64[ns](1), float64(1), object(4)\n", "memory usage: 162.1+ KB\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  budget_eur project_id  country\n", "0 2023-01-31         HR           Salaries     2293.00       BE55  Belgium\n", "1 2023-01-31         HR           Training     4768.76       BE55  Belgium\n", "2 2023-01-31         HR        Recruitment     1642.21       BE55  Belgium\n", "3 2023-01-31    Medical        Medications     1377.75       BE55  Belgium\n", "4 2023-01-31    Medical  Medical Equipment     2062.46       BE55  Belgium"]}, "execution_count": 180, "metadata": {}, "output_type": "execute_result"}], "source": ["budget_df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_budget_data.parquet')\n", "print(budget_df.isnull().sum())\n", "budget_df.info()\n", "budget_df.head()"]}, {"cell_type": "code", "execution_count": 181, "id": "645a61d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["date                   0\n", "department             0\n", "category               0\n", "amount_local         576\n", "project_id             0\n", "country                0\n", "original_currency      0\n", "rate                   0\n", "amount_eur           576\n", "dtype: int64\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 9 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   date               3456 non-null   datetime64[ns]\n", " 1   department         3456 non-null   object        \n", " 2   category           3456 non-null   object        \n", " 3   amount_local       2880 non-null   float64       \n", " 4   project_id         3456 non-null   object        \n", " 5   country            3456 non-null   object        \n", " 6   original_currency  3456 non-null   object        \n", " 7   rate               3456 non-null   float64       \n", " 8   amount_eur         2880 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(3), object(5)\n", "memory usage: 243.1+ KB\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>rate</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>2278.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>4053.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>1450.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>1275.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>2182.81</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  rate  amount_eur  \n", "0               EUR   1.0     2278.71  \n", "1               EUR   1.0     4053.43  \n", "2               EUR   1.0     1450.10  \n", "3               EUR   1.0     1275.95  \n", "4               EUR   1.0     2182.81  "]}, "execution_count": 181, "metadata": {}, "output_type": "execute_result"}], "source": ["# read the expenses df\n", "expenses_df = pd.read_parquet('/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet')\n", "print(expenses_df.isnull().sum())\n", "expenses_df.info()\n", "expenses_df.head()"]}, {"cell_type": "code", "execution_count": 183, "id": "220e5c82", "metadata": {}, "outputs": [{"data": {"text/plain": ["3456"]}, "execution_count": 183, "metadata": {}, "output_type": "execute_result"}], "source": ["len(expenses_df)\n"]}, {"cell_type": "code", "execution_count": 188, "id": "a6396e65", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BE55', 'SN02', 'BF01', 'BE01', 'BF02', 'SN01', 'KE01', 'KEO2'],\n", "      dtype=object)"]}, "execution_count": 188, "metadata": {}, "output_type": "execute_result"}], "source": ["budget_df['project_id'].unique()"]}, {"cell_type": "code", "execution_count": 190, "id": "d0662c9b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['BE55', 'BE01', 'BF02', 'KE02', 'SN02', 'SN01', 'KE01', 'BF01'],\n", "      dtype=object)"]}, "execution_count": 190, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df['project_id'].unique() "]}, {"cell_type": "code", "execution_count": 196, "id": "ed6a2481", "metadata": {}, "outputs": [], "source": ["a = set(budget_df['project_id'].unique())"]}, {"cell_type": "code", "execution_count": 197, "id": "8c5c671f", "metadata": {}, "outputs": [], "source": ["b = set(expenses_df['project_id'].unique())"]}, {"cell_type": "code", "execution_count": 198, "id": "52c366c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'BE01', 'BE55', 'SN01', 'KE01', 'BF02', 'SN02', 'KEO2', 'BF01'}\n", "{'BE01', 'BE55', 'SN01', 'KE01', 'BF02', 'SN02', 'BF01', 'KE02'}\n"]}], "source": ["print(a)\n", "print(b)"]}, {"cell_type": "code", "execution_count": 194, "id": "859535c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 194, "metadata": {}, "output_type": "execute_result"}], "source": ["# find uniqye project ids in the expenses df\n", "set(budget_df['project_id'].unique()) == set(expenses_df['project_id'].unique())"]}, {"cell_type": "code", "execution_count": 199, "id": "6b17be9f", "metadata": {}, "outputs": [], "source": ["# convert the 'KEO2' to 'KE02' in the budget df project id column\n", "budget_df['project_id'] = budget_df['project_id'].replace('KEO2', 'KE02')"]}, {"cell_type": "code", "execution_count": 201, "id": "f51ac42b", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 201, "metadata": {}, "output_type": "execute_result"}], "source": ["a =set(budget_df['project_id'].unique())\n", "b =set(expenses_df['project_id'].unique())\n", "a == b"]}, {"cell_type": "code", "execution_count": 204, "id": "ced7dd76", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>rate</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>2278.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>4053.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>1450.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>1275.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>2182.81</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  rate  amount_eur  \n", "0               EUR   1.0     2278.71  \n", "1               EUR   1.0     4053.43  \n", "2               EUR   1.0     1450.10  \n", "3               EUR   1.0     1275.95  \n", "4               EUR   1.0     2182.81  "]}, "execution_count": 204, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "9059ab22", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ee080f68", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b42b3dd7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 202, "id": "395a57e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  budget_eur project_id  country\n", "0 2023-01-31         HR           Salaries     2293.00       BE55  Belgium\n", "1 2023-01-31         HR           Training     4768.76       BE55  Belgium\n", "2 2023-01-31         HR        Recruitment     1642.21       BE55  Belgium\n", "3 2023-01-31    Medical        Medications     1377.75       BE55  Belgium\n", "4 2023-01-31    Medical  Medical Equipment     2062.46       BE55  Belgium"]}, "execution_count": 202, "metadata": {}, "output_type": "execute_result"}], "source": ["budget_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "23baa888", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 206, "id": "af832b1c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>rate</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>2278.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>4053.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>1450.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>1275.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>1.0</td>\n", "      <td>2182.81</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  amount_local project_id  country  \\\n", "0 2023-01-31         HR           Salaries       2278.71       BE55  Belgium   \n", "1 2023-01-31         HR           Training       4053.43       BE55  Belgium   \n", "2 2023-01-31         HR        Recruitment       1450.10       BE55  Belgium   \n", "3 2023-01-31    Medical        Medications       1275.95       BE55  Belgium   \n", "4 2023-01-31    Medical  Medical Equipment       2182.81       BE55  Belgium   \n", "\n", "  original_currency  rate  amount_eur  \n", "0               EUR   1.0     2278.71  \n", "1               EUR   1.0     4053.43  \n", "2               EUR   1.0     1450.10  \n", "3               EUR   1.0     1275.95  \n", "4               EUR   1.0     2182.81  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-31</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-31</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date department           category  budget_eur project_id  country\n", "0 2023-01-31         HR           Salaries     2293.00       BE55  Belgium\n", "1 2023-01-31         HR           Training     4768.76       BE55  Belgium\n", "2 2023-01-31         HR        Recruitment     1642.21       BE55  Belgium\n", "3 2023-01-31    Medical        Medications     1377.75       BE55  Belgium\n", "4 2023-01-31    Medical  Medical Equipment     2062.46       BE55  Belgium"]}, "execution_count": 206, "metadata": {}, "output_type": "execute_result"}], "source": ["silver_expenses_df = expenses_df\n", "silver_budget_df = budget_df\n", "display(silver_expenses_df.head())\n", "silver_budget_df.head()"]}, {"cell_type": "code", "execution_count": 210, "id": "64c06e86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3456\n", "3456\n"]}], "source": ["print(len(silver_expenses_df))\n", "print(len(silver_budget_df))"]}, {"cell_type": "code", "execution_count": 207, "id": "2fcd8641", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Gold Layer: Starting Data Modeling for Power BI ---\n"]}], "source": ["# --- Section 3: Gold Layer - Curated & Optimized Data for Reporting ---\n", "\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "import calendar # For getting days in month if needed for date dimension (though pd.date_range is easier)\n", "\n", "# Assuming silver_expenses_dfa and silver_budget_df are available from previous cells\n", "# Assuming OUTPUT_DIRECTORY is defined from global configurations\n", "\n", "print(\"\\n--- Gold Layer: Starting Data Modeling for Power BI ---\")\n"]}, {"cell_type": "code", "execution_count": 215, "id": "1cb13ca0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Gold Layer: Starting Data Modeling for Power BI ---\n", "  Generating Date Dimension for month-ends from 2023-01 to 2025-12\n", "  Date Dimension created with 36 rows (expected 36 for 3 years * 12 months).\n", "\n", "--- Date Dimension DataFrame Info ---\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 36 entries, 0 to 35\n", "Data columns (total 12 columns):\n", " #   Column            Non-Null Count  Dtype         \n", "---  ------            --------------  -----         \n", " 0   date_key          36 non-null     int64         \n", " 1   date              36 non-null     datetime64[ns]\n", " 2   year              36 non-null     int32         \n", " 3   month_num         36 non-null     int32         \n", " 4   month_name        36 non-null     object        \n", " 5   month_name_full   36 non-null     object        \n", " 6   quarter           36 non-null     int32         \n", " 7   day_of_week_num   36 non-null     int32         \n", " 8   day_of_week_name  36 non-null     object        \n", " 9   day_of_month      36 non-null     int32         \n", " 10  week_of_year      36 non-null     int64         \n", " 11  year_month_key    36 non-null     int64         \n", "dtypes: datetime64[ns](1), int32(5), int64(3), object(3)\n", "memory usage: 2.8+ KB\n", "\n", "--- Date Dimension DataFrame Head ---\n", "   date_key       date  year  month_num month_name month_name_full  quarter  \\\n", "0  20230131 2023-01-31  2023          1        Jan         January        1   \n", "1  20230228 2023-02-28  2023          2        Feb        February        1   \n", "2  20230331 2023-03-31  2023          3        Mar           March        1   \n", "3  20230430 2023-04-30  2023          4        Apr           April        2   \n", "4  20230531 2023-05-31  2023          5        May             May        2   \n", "\n", "   day_of_week_num day_of_week_name  day_of_month  week_of_year  \\\n", "0                1          Tuesday            31             5   \n", "1                1          Tuesday            28             9   \n", "2                4           Friday            31            13   \n", "3                6           Sunday            30            17   \n", "4                2        Wednesday            31            22   \n", "\n", "   year_month_key  \n", "0          202301  \n", "1          202302  \n", "2          202303  \n", "3          202304  \n", "4          202305  \n", "\n", "--- Date Dimension DataFrame Tail ---\n", "    date_key       date  year  month_num month_name month_name_full  quarter  \\\n", "31  20250831 2025-08-31  2025          8        Aug          August        3   \n", "32  20250930 2025-09-30  2025          9        Sep       September        3   \n", "33  20251031 2025-10-31  2025         10        Oct         October        4   \n", "34  20251130 2025-11-30  2025         11        Nov        November        4   \n", "35  20251231 2025-12-31  2025         12        Dec        December        4   \n", "\n", "    day_of_week_num day_of_week_name  day_of_month  week_of_year  \\\n", "31                6           Sunday            31            35   \n", "32                1          Tuesday            30            40   \n", "33                4           Friday            31            44   \n", "34                6           Sunday            30            48   \n", "35                2        Wednesday            31             1   \n", "\n", "    year_month_key  \n", "31          202508  \n", "32          202509  \n", "33          202510  \n", "34          202511  \n", "35          202512  \n", "\n", "--- Gold Layer: Creating Fact Analysis Table ---\n", "  Combined expenses (3456 rows) and budgets (3456 rows).\n", "  Filled NaN values in 'amount_eur_final' with 0.\n", "  Fact Analysis Table created with 6912 rows.\n", "\n", "--- Fact Analysis DataFrame Info ---\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 6912 entries, 0 to 6911\n", "Data columns (total 9 columns):\n", " #   Column            Non-Null Count  Dtype         \n", "---  ------            --------------  -----         \n", " 0   date_key          6912 non-null   int64         \n", " 1   year_month_key    6912 non-null   int64         \n", " 2   date              6912 non-null   datetime64[ns]\n", " 3   record_type       6912 non-null   object        \n", " 4   amount_eur_final  6912 non-null   float64       \n", " 5   project_id        6912 non-null   object        \n", " 6   country           6912 non-null   object        \n", " 7   department        6912 non-null   object        \n", " 8   category          6912 non-null   object        \n", "dtypes: datetime64[ns](1), float64(1), int64(2), object(5)\n", "memory usage: 486.1+ KB\n", "\n", "--- Fact Analysis DataFrame Head ---\n", "   date_key  year_month_key       date record_type  amount_eur_final  \\\n", "0  20230131          202301 2023-01-31     Expense           2278.71   \n", "1  20230131          202301 2023-01-31     Expense           4053.43   \n", "2  20230131          202301 2023-01-31     Expense           1450.10   \n", "3  20230131          202301 2023-01-31     Expense           1275.95   \n", "4  20230131          202301 2023-01-31     Expense           2182.81   \n", "\n", "  project_id  country department           category  \n", "0       BE55  Belgium         HR           Salaries  \n", "1       BE55  Belgium         HR           Training  \n", "2       BE55  Belgium         HR        Recruitment  \n", "3       BE55  Belgium    Medical        Medications  \n", "4       BE55  Belgium    Medical  Medical Equipment  \n", "\n", "--- Fact Analysis DataFrame Tail (to check 2025-Jul-Dec for expenses) ---\n", "      date_key  year_month_key       date record_type  amount_eur_final  \\\n", "6907  20251231          202512 2025-12-31      Budget           4017.93   \n", "6908  20251231          202512 2025-12-31      Budget           2099.59   \n", "6909  20251231          202512 2025-12-31      Budget           4371.15   \n", "6910  20251231          202512 2025-12-31      Budget           2842.26   \n", "6911  20251231          202512 2025-12-31      Budget           4898.71   \n", "\n", "     project_id country department             category  \n", "6907       KE02   Kenya  Logistics            Transport  \n", "6908       KE02   Kenya  Logistics  Vehicle Maintenance  \n", "6909       KE02   Kenya     Supply             Supplies  \n", "6910       KE02   Kenya     Supply          Warehousing  \n", "6911       KE02   Kenya     Supply           Cold Chain  \n", "\n", "--- Gold Layer: Saving Output to Parquet Files ---\n", "  Fact Analysis Table saved to: /Users/<USER>/test/MSF-test/processed_data/fact_analysis_table.parquet\n", "  Date Dimension Table saved to: /Users/<USER>/test/MSF-test/processed_data/dim_date_table.parquet\n", "\n", "--- Gold Layer: Completed ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/75/h2009njj4hn9559qy5db4rvh0000gn/T/ipykernel_2064/3372353758.py:26: FutureWarning: 'M' is deprecated and will be removed in a future version, please use 'ME' instead.\n", "  date_range = pd.date_range(start=min_date.replace(day=1), end=max_date, freq='M')\n"]}], "source": ["# --- Section 3: Gold Layer - Curated & Optimized Data for Reporting ---\n", "\n", "import pandas as pd\n", "from datetime import datetime\n", "from pandas.tseries.offsets import MonthEnd # Import MonthEnd for monthly frequency\n", "\n", "# Assuming silver_expenses_df and silver_budget_df are available from previous cells\n", "# Assuming OUTPUT_DIRECTORY is defined from global configurations\n", "\n", "print(\"\\n--- Gold Layer: Starting Data Modeling for Power BI ---\")\n", "\n", "# --- Step 1: Create the Date Dimension Table (dim_date_df) - CORRECTED FOR MONTHLY GRANULARITY ---\n", "# Determine the overall date range from both expenses and budget data.\n", "# We need to cover all months from the earliest record to the latest budget record (Dec 2025).\n", "min_date = min(silver_expenses_df['date'].min(), silver_budget_df['date'].min())\n", "max_date = max(silver_expenses_df['date'].max(), silver_budget_df['date'].max())\n", "\n", "# Generate a date range specifically for the last day of each month\n", "# Start from the 1st day of the min month, and end at the last day of the max month.\n", "# Then, use 'M' frequency to get month-end dates.\n", "print(f\"  Generating Date Dimension for month-ends from {min_date.strftime('%Y-%m')} to {max_date.strftime('%Y-%m')}\")\n", "\n", "# Create a range of month-end dates\n", "# The 'M' frequency generates month-end dates.\n", "# Corrected: The 'end' date should be the max_date itself, as 'freq='M'' will correctly find its month end.\n", "date_range = pd.date_range(start=min_date.replace(day=1), end=max_date, freq='M')\n", "dim_date_df = pd.DataFrame({'date': date_range})\n", "\n", "dim_date_df['date_key'] = dim_date_df['date'].dt.strftime('%Y%m%d').astype(int)\n", "dim_date_df['year'] = dim_date_df['date'].dt.year\n", "dim_date_df['month_num'] = dim_date_df['date'].dt.month\n", "dim_date_df['month_name'] = dim_date_df['date'].dt.strftime('%b') # Abbreviated month name (Jan, Feb)\n", "dim_date_df['month_name_full'] = dim_date_df['date'].dt.strftime('%B') # Full month name (January, February)\n", "dim_date_df['quarter'] = dim_date_df['date'].dt.quarter\n", "# For monthly granularity, day_of_week_num, day_of_week_name, day_of_month, week_of_year\n", "# might be less relevant or refer to the last day of the month.\n", "# Let's include them as they refer to the 'date' column which is month-end.\n", "dim_date_df['day_of_week_num'] = dim_date_df['date'].dt.dayofweek # Monday=0, Sunday=6\n", "dim_date_df['day_of_week_name'] = dim_date_df['date'].dt.day_name()\n", "dim_date_df['day_of_month'] = dim_date_df['date'].dt.day # This will be the last day of the month\n", "dim_date_df['week_of_year'] = dim_date_df['date'].dt.isocalendar().week.astype(int)\n", "dim_date_df['year_month_key'] = dim_date_df['date'].dt.strftime('%Y%m').astype(int) #MM for monthly joins\n", "\n", "# Select and reorder columns for the final Date Dimension\n", "dim_date_df = dim_date_df[[\n", "    'date_key', 'date', 'year', 'month_num', 'month_name', 'month_name_full',\n", "    'quarter', 'day_of_week_num', 'day_of_week_name', 'day_of_month', 'week_of_year', 'year_month_key'\n", "]]\n", "\n", "print(f\"  Date Dimension created with {len(dim_date_df)} rows (expected 36 for 3 years * 12 months).\")\n", "print(\"\\n--- Date Dimension DataFrame Info ---\")\n", "dim_date_df.info()\n", "print(\"\\n--- Date Dimension DataFrame Head ---\")\n", "print(dim_date_df.head())\n", "print(\"\\n--- Date Dimension DataFrame Tail ---\")\n", "print(dim_date_df.tail())\n", "\n", "\n", "# --- Step 2: Create the Fact Analysis Table (fact_analysis_df) ---\n", "print(\"\\n--- Gold Layer: Creating Fact Analysis Table ---\")\n", "\n", "# Prepare expenses data for concatenation\n", "# We only need the date, dimensions, and the converted amount_eur\n", "fact_expenses = silver_expenses_df[[\n", "    'date', 'department', 'category', 'project_id', 'country', 'amount_eur'\n", "]].copy()\n", "fact_expenses['record_type'] = 'Expense'\n", "fact_expenses = fact_expenses.rename(columns={'amount_eur': 'amount_eur_final'})\n", "\n", "# Prepare budget data for concatenation\n", "# We only need the date, dimensions, and the budget_eur\n", "fact_budget = silver_budget_df[[\n", "    'date', 'department', 'category', 'project_id', 'country', 'budget_eur'\n", "]].copy()\n", "fact_budget['record_type'] = 'Budget'\n", "fact_budget = fact_budget.rename(columns={'budget_eur': 'amount_eur_final'})\n", "\n", "# Concatenate the two DataFrames vertically\n", "fact_analysis_df = pd.concat([fact_expenses, fact_budget], ignore_index=True)\n", "print(f\"  Combined expenses ({len(fact_expenses)} rows) and budgets ({len(fact_budget)} rows).\")\n", "\n", "# Fill any NaN values in 'amount_eur_final' with 0.\n", "# This is crucial for the 2025 Jul-Dec expenses where actuals are blank,\n", "# ensuring they appear as 0 in Power BI sums.\n", "fact_analysis_df['amount_eur_final'] = fact_analysis_df['amount_eur_final'].fillna(0)\n", "print(\"  Filled NaN values in 'amount_eur_final' with 0.\")\n", "\n", "# Add date_key for linking to Date Dimension (using the date column from fact table)\n", "fact_analysis_df['date_key'] = fact_analysis_df['date'].dt.strftime('%Y%m%d').astype(int)\n", "fact_analysis_df['year_month_key'] = fact_analysis_df['date'].dt.strftime('%Y%m').astype(int) # For monthly aggregation/filtering\n", "\n", "# Reorder columns for the final Fact Table (optional, but good for consistency)\n", "fact_analysis_df = fact_analysis_df[[\n", "    'date_key', 'year_month_key', 'date', 'record_type', 'amount_eur_final',\n", "    'project_id', 'country', 'department', 'category'\n", "]]\n", "\n", "print(f\"  Fact Analysis Table created with {len(fact_analysis_df)} rows.\")\n", "print(\"\\n--- Fact Analysis DataFrame Info ---\")\n", "fact_analysis_df.info()\n", "print(\"\\n--- Fact Analysis DataFrame Head ---\")\n", "print(fact_analysis_df.head())\n", "print(\"\\n--- Fact Analysis DataFrame Tail (to check 2025-Jul-Dec for expenses) ---\")\n", "print(fact_analysis_df.tail())\n", "\n", "\n", "# --- Step 3: Save Gold Layer Data to Parquet Files ---\n", "print(\"\\n--- Gold Layer: Saving Output to Parquet Files ---\")\n", "\n", "# Ensure the output directory exists (already handled in config, but good for standalone test)\n", "os.makedirs(OUTPUT_DIRECTORY, exist_ok=True)\n", "\n", "fact_output_path = os.path.join(OUTPUT_DIRECTORY, \"fact_analysis_table.parquet\")\n", "dim_date_output_path = os.path.join(OUTPUT_DIRECTORY, \"dim_date_table.parquet\")\n", "\n", "if not fact_analysis_df.empty:\n", "    fact_analysis_df.to_parquet(fact_output_path, index=False)\n", "    print(f\"  Fact Analysis Table saved to: {fact_output_path}\")\n", "else:\n", "    print(\"  Fact Analysis DataFrame is empty, skipping save.\")\n", "\n", "if not dim_date_df.empty:\n", "    dim_date_df.to_parquet(dim_date_output_path, index=False)\n", "    print(f\"  Date Dimension Table saved to: {dim_date_output_path}\")\n", "else:\n", "    print(\"  Date Dimension DataFrame is empty, skipping save.\")\n", "\n", "print(\"\\n--- Gold Layer: Completed ---\")"]}, {"cell_type": "code", "execution_count": 219, "id": "046e9db5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date_key</th>\n", "      <th>date</th>\n", "      <th>year</th>\n", "      <th>month_num</th>\n", "      <th>month_name</th>\n", "      <th>month_name_full</th>\n", "      <th>quarter</th>\n", "      <th>day_of_week_num</th>\n", "      <th>day_of_week_name</th>\n", "      <th>day_of_month</th>\n", "      <th>week_of_year</th>\n", "      <th>year_month_key</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20230131</td>\n", "      <td>2023-01-31</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Jan</td>\n", "      <td>January</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Tuesday</td>\n", "      <td>31</td>\n", "      <td>5</td>\n", "      <td>202301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20230228</td>\n", "      <td>2023-02-28</td>\n", "      <td>2023</td>\n", "      <td>2</td>\n", "      <td>Feb</td>\n", "      <td>February</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Tuesday</td>\n", "      <td>28</td>\n", "      <td>9</td>\n", "      <td>202302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20230331</td>\n", "      <td>2023-03-31</td>\n", "      <td>2023</td>\n", "      <td>3</td>\n", "      <td>Mar</td>\n", "      <td>March</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>Friday</td>\n", "      <td>31</td>\n", "      <td>13</td>\n", "      <td>202303</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20230430</td>\n", "      <td>2023-04-30</td>\n", "      <td>2023</td>\n", "      <td>4</td>\n", "      <td>Apr</td>\n", "      <td>April</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>Sunday</td>\n", "      <td>30</td>\n", "      <td>17</td>\n", "      <td>202304</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20230531</td>\n", "      <td>2023-05-31</td>\n", "      <td>2023</td>\n", "      <td>5</td>\n", "      <td>May</td>\n", "      <td>May</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Wednesday</td>\n", "      <td>31</td>\n", "      <td>22</td>\n", "      <td>202305</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   date_key       date  year  month_num month_name month_name_full  quarter  \\\n", "0  20230131 2023-01-31  2023          1        Jan         January        1   \n", "1  20230228 2023-02-28  2023          2        Feb        February        1   \n", "2  20230331 2023-03-31  2023          3        Mar           March        1   \n", "3  20230430 2023-04-30  2023          4        Apr           April        2   \n", "4  20230531 2023-05-31  2023          5        May             May        2   \n", "\n", "   day_of_week_num day_of_week_name  day_of_month  week_of_year  \\\n", "0                1          Tuesday            31             5   \n", "1                1          Tuesday            28             9   \n", "2                4           Friday            31            13   \n", "3                6           Sunday            30            17   \n", "4                2        Wednesday            31            22   \n", "\n", "   year_month_key  \n", "0          202301  \n", "1          202302  \n", "2          202303  \n", "3          202304  \n", "4          202305  "]}, "execution_count": 219, "metadata": {}, "output_type": "execute_result"}], "source": ["date_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/dim_date_table.parquet\")\n", "date_df.head()"]}, {"cell_type": "code", "execution_count": 220, "id": "c961737d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date_key</th>\n", "      <th>year_month_key</th>\n", "      <th>date</th>\n", "      <th>record_type</th>\n", "      <th>amount_eur_final</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20230131</td>\n", "      <td>202301</td>\n", "      <td>2023-01-31</td>\n", "      <td>Expense</td>\n", "      <td>2278.71</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20230131</td>\n", "      <td>202301</td>\n", "      <td>2023-01-31</td>\n", "      <td>Expense</td>\n", "      <td>4053.43</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20230131</td>\n", "      <td>202301</td>\n", "      <td>2023-01-31</td>\n", "      <td>Expense</td>\n", "      <td>1450.10</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20230131</td>\n", "      <td>202301</td>\n", "      <td>2023-01-31</td>\n", "      <td>Expense</td>\n", "      <td>1275.95</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20230131</td>\n", "      <td>202301</td>\n", "      <td>2023-01-31</td>\n", "      <td>Expense</td>\n", "      <td>2182.81</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   date_key  year_month_key       date record_type  amount_eur_final  \\\n", "0  20230131          202301 2023-01-31     Expense           2278.71   \n", "1  20230131          202301 2023-01-31     Expense           4053.43   \n", "2  20230131          202301 2023-01-31     Expense           1450.10   \n", "3  20230131          202301 2023-01-31     Expense           1275.95   \n", "4  20230131          202301 2023-01-31     Expense           2182.81   \n", "\n", "  project_id  country department           category  \n", "0       BE55  Belgium         HR           Salaries  \n", "1       BE55  Belgium         HR           Training  \n", "2       BE55  Belgium         HR        Recruitment  \n", "3       BE55  Belgium    Medical        Medications  \n", "4       BE55  Belgium    Medical  Medical Equipment  "]}, "execution_count": 220, "metadata": {}, "output_type": "execute_result"}], "source": ["fact_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/fact_analysis_table.parquet\")\n", "fact_df.head()"]}, {"cell_type": "code", "execution_count": 221, "id": "dd843fc5", "metadata": {}, "outputs": [{"data": {"text/plain": ["6912"]}, "execution_count": 221, "metadata": {}, "output_type": "execute_result"}], "source": ["len(fact_df)"]}, {"cell_type": "code", "execution_count": null, "id": "7f792cda", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 223, "id": "840ebc4a", "metadata": {}, "outputs": [], "source": ["## Get my silver dfs\n", "silver_expenses_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/silver_expenses_data.parquet\")\n", "silver_budget_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/silver_budget_data.parquet\")"]}, {"cell_type": "code", "execution_count": 227, "id": "a27218a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["date          0\n", "department    0\n", "category      0\n", "budget_eur    0\n", "project_id    0\n", "country       0\n", "dtype: int64"]}, "execution_count": 227, "metadata": {}, "output_type": "execute_result"}], "source": ["silver_budget_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 228, "id": "ab678425", "metadata": {}, "outputs": [{"data": {"text/plain": ["date                   0\n", "department             0\n", "category               0\n", "amount_local         576\n", "project_id             0\n", "country                0\n", "original_currency      0\n", "rate                   0\n", "amount_eur           576\n", "dtype: int64"]}, "execution_count": 228, "metadata": {}, "output_type": "execute_result"}], "source": ["expenses_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 229, "id": "f38b3867", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3456 entries, 0 to 3455\n", "Data columns (total 9 columns):\n", " #   Column             Non-Null Count  Dtype         \n", "---  ------             --------------  -----         \n", " 0   date               3456 non-null   datetime64[ns]\n", " 1   department         3456 non-null   object        \n", " 2   category           3456 non-null   object        \n", " 3   amount_local       2880 non-null   float64       \n", " 4   project_id         3456 non-null   object        \n", " 5   country            3456 non-null   object        \n", " 6   original_currency  3456 non-null   object        \n", " 7   rate               3456 non-null   float64       \n", " 8   amount_eur         2880 non-null   float64       \n", "dtypes: datetime64[ns](1), float64(3), object(5)\n", "memory usage: 243.1+ KB\n"]}], "source": ["expenses_df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "50395580", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "58b24c3b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4e672de1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "def84240", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7378d056", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 230, "id": "d300784a", "metadata": {}, "outputs": [], "source": ["# RESTART\n"]}, {"cell_type": "code", "execution_count": 231, "id": "b9ae5d6d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>amount_local</th>\n", "      <th>currency</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "      <th>original_currency</th>\n", "      <th>amount_eur</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2278.71</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4053.43</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1450.10</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1275.95</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>01</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2182.81</td>\n", "      <td>EUR</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year month department           category  amount_local currency  \\\n", "0   1  2023    01         HR           Salaries       2278.71      EUR   \n", "1   2  2023    01         HR           Training       4053.43      EUR   \n", "2   3  2023    01         HR        Recruitment       1450.10      EUR   \n", "3   4  2023    01    Medical        Medications       1275.95      EUR   \n", "4   5  2023    01    Medical  Medical Equipment       2182.81      EUR   \n", "\n", "  project_id  country original_currency  amount_eur  \n", "0       BE55  Belgium               EUR         NaN  \n", "1       BE55  Belgium               EUR         NaN  \n", "2       BE55  Belgium               EUR         NaN  \n", "3       BE55  Belgium               EUR         NaN  \n", "4       BE55  Belgium               EUR         NaN  "]}, "execution_count": 231, "metadata": {}, "output_type": "execute_result"}], "source": ["bronze_expenses_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/bronze_expenses_data.parquet\")\n", "bronze_expenses_df.head()"]}, {"cell_type": "code", "execution_count": 234, "id": "53e6a24c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'BE01': {'country': 'Belgium', 'currency': 'EUR'},\n", " 'BE55': {'country': 'Belgium', 'currency': 'EUR'},\n", " 'KE01': {'country': 'Kenya', 'currency': 'KES'},\n", " 'KE02': {'country': 'Kenya', 'currency': 'KES'},\n", " 'SN01': {'country': 'Senegal', 'currency': 'XOF'},\n", " 'SN02': {'country': 'Senegal', 'currency': 'XOF'},\n", " 'BF01': {'country': 'Burkina Faso', 'currency': 'XOF'},\n", " 'BF02': {'country': 'Burkina Faso', 'currency': 'XOF'}}"]}, "execution_count": 234, "metadata": {}, "output_type": "execute_result"}], "source": ["PROJECT_CURRENCY_MAP"]}, {"cell_type": "code", "execution_count": 235, "id": "1e99fba2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>department</th>\n", "      <th>category</th>\n", "      <th>budget_eur</th>\n", "      <th>version</th>\n", "      <th>project_id</th>\n", "      <th>country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Salaries</td>\n", "      <td>2293.00</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Training</td>\n", "      <td>4768.76</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>HR</td>\n", "      <td>Recruitment</td>\n", "      <td>1642.21</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medications</td>\n", "      <td>1377.75</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2023</td>\n", "      <td>1</td>\n", "      <td>Medical</td>\n", "      <td>Medical Equipment</td>\n", "      <td>2062.46</td>\n", "      <td>v1</td>\n", "      <td>BE55</td>\n", "      <td>Belgium</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  year  month department           category  budget_eur version  \\\n", "0   1  2023      1         HR           Salaries     2293.00      v1   \n", "1   2  2023      1         HR           Training     4768.76      v1   \n", "2   3  2023      1         HR        Recruitment     1642.21      v1   \n", "3   4  2023      1    Medical        Medications     1377.75      v1   \n", "4   5  2023      1    Medical  Medical Equipment     2062.46      v1   \n", "\n", "  project_id  country  \n", "0       BE55  Belgium  \n", "1       BE55  Belgium  \n", "2       BE55  Belgium  \n", "3       BE55  Belgium  \n", "4       BE55  Belgium  "]}, "execution_count": 235, "metadata": {}, "output_type": "execute_result"}], "source": ["bronze_budget_df = pd.read_parquet(\"/Users/<USER>/test/MSF-test/processed_data/bronze_budget_data.parquet\")\n", "bronze_budget_df.head()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}