import pandas as pd
from gold_layer import gold

# Accessing the gold_df from the gold layer (Medallion Architecture)
gold_df = gold()

# saving the gold_df to a csv file
path = "/Users/<USER>/test/MSF-test2/processed_data/gold_df.csv"
gold_df.to_csv(path, index=False)

# Load your final Gold layer data
df = pd.read_csv('gold_df.csv')

# Convert 'date' to YYYYMMDD integer format
df['date_id'] = pd.to_datetime(df['date']).dt.strftime('%Y%m%d').astype(int)

# Build dimension tables
dim_date = df[['date_id', 'date']].drop_duplicates().reset_index(drop=True)

dim_department = df[['department']].drop_duplicates().reset_index(drop=True)
dim_department['department_id'] = dim_department.index + 1

dim_country = df[['country']].drop_duplicates().reset_index(drop=True)
dim_country['country_id'] = dim_country.index + 1

dim_category = df[['category']].drop_duplicates().reset_index(drop=True)
dim_category['category_id'] = dim_category.index + 1

dim_project = df[['project_id']].drop_duplicates().reset_index(drop=True)
dim_project['project_id_numeric'] = dim_project.index + 1

# Merge keys back into the main dataframe
df = df.merge(dim_department, on='department', how='left') \
       .merge(dim_country, on='country', how='left') \
       .merge(dim_category, on='category', how='left') \
       .merge(dim_project, on='project_id', how='left')

# Fact Table
fact_expenses = df[['date_id', 'department_id', 'country_id', 'category_id', 'project_id_numeric', 'budget_eur', 'amount_eur']]

# Save to CSVs
dim_date.to_csv('dim_date.csv', index=False)
dim_department.to_csv('dim_department.csv', index=False)
dim_country.to_csv('dim_country.csv', index=False)
dim_category.to_csv('dim_category.csv', index=False)
dim_project.to_csv('dim_project.csv', index=False)
fact_expenses.to_csv('fact_expenses.csv', index=False)

