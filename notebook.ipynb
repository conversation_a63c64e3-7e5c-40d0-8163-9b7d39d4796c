

def bronze_budget():

    # This dictionary contains the TRUTH for project country and currency,
    # overriding any inconsistencies found in source data.
    PROJECT_CURRENCY_MAP = {
        "BE01": {"country": "Belgium", "currency": "EUR"},
        "BE55": {"country": "Belgium", "currency": "EUR"},
        "KE01": {"country": "Kenya", "currency": "KES"},   # Corrected: Force KES for KE01 expenses, overriding DB's XOF
        "KEO2": {"country": "Kenya", "currency": "KES"},
        "SN01": {"country": "Senegal", "currency": "XOF"}, # Corrected: Force Senegal for SN01 country, overriding DB's Kenya
        "SN02": {"country": "Senegal", "currency": "XOF"},
        "BF01": {"country": "Burkina Faso", "currency": "XOF"},
        "BF02": {"country": "Burkina Faso", "currency": "XOF"},
    }

    DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
    OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

    # Read csvs (budget data)
    import pandas as pd
    import os

    # --- Function to extract budget from a single CSV ---
    def extract_budget_from_csv(csv_path: str) -> pd.DataFrame:
        """
        Reads budget data from a CSV file.
        """
        try:
            df = pd.read_csv(csv_path)
            return df
        except Exception as e:
            print(f"Error reading budget from {csv_path}: {e}")
            return pd.DataFrame()
        
    # --- Main logic to iterate and combine CSVs ---
    all_budget_df = pd.DataFrame()
    csv_files_found = 0

    print(f"\n--- Bronze Layer: Starting CSV Data Ingestion from {DATA_DIRECTORY} ---")

    if not os.path.exists(DATA_DIRECTORY):
        print(f"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.")
    else:
        for filename in os.listdir(DATA_DIRECTORY):
            file_path = os.path.join(DATA_DIRECTORY, filename)
            
            # Process only files ending with '_budget.csv' and skip directories
            if os.path.isdir(file_path) or not filename.endswith("_budget.csv"):
                continue

            # Derive project_id from filename (e.g., "BE01" from "BE01_budget.csv")
            project_name_from_file = os.path.splitext(filename)[0]
            print(f"project_name_from_file: {project_name_from_file}")
            base_project_id = project_name_from_file.replace('_budget', '')

            # Get country from our PROJECT_CURRENCY_MAP
            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)
            if not project_info:
                print(f"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.")
                continue
            
            country_from_map = project_info['country']

            print(f"  Ingesting budget from CSV: {filename} (Project: {base_project_id}, Country: {country_from_map})...")
            df = extract_budget_from_csv(file_path)
            # print(f"df: \n{df.head()}")
            
            if not df.empty:
                df['project_id'] = base_project_id
                df['country'] = country_from_map
                # Also add the original_currency_map for consistency, even if budget is already in EUR
                all_budget_df = pd.concat([all_budget_df, df], ignore_index=True)
                csv_files_found += 1
            else:
                print(f" No data extracted or error occurred for {filename}.")

    return all_budget_df


budget_df = bronze_budget()


budget_df.head()




import pandas as pd
import sqlite3
import os

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

def bronze_expenses()-> pd.DataFrame:
    # --- Bronze Layer: Ingesting Expenses from SQLite DBs ---
    def extract_expenses_from_db(db_path: str) -> pd.DataFrame:
        """
        Connects to a SQLite database and extracts all data from the 'expenses' table.
        """
        conn = None
        try:
            conn = sqlite3.connect(db_path)
            query = "SELECT * FROM expenses" # Assumes 'expenses' is the table name
            df = pd.read_sql_query(query, conn)
            return df
        except sqlite3.Error as e:
            print(f"Error reading expenses from {db_path}: {e}")
            return pd.DataFrame() # Return empty DataFrame on error
        finally:
            if conn:
                conn.close()


    # --- Main logic to iterate and combine DBs ---
    all_expenses_df = pd.DataFrame()
    db_files_found = 0

    # Define project currency map for expenses
    PROJECT_CURRENCY_MAP = {
        "BE01": {"country": "Belgium", "currency": "EUR"},
        "BE55": {"country": "Belgium", "currency": "EUR"},
        "KE01": {"country": "Kenya", "currency": "KES"},
        "KE02": {"country": "Kenya", "currency": "KES"},   
        "SN01": {"country": "Senegal", "currency": "XOF"},
        "SN02": {"country": "Senegal", "currency": "XOF"},
        "BF01": {"country": "Burkina Faso", "currency": "XOF"},
        "BF02": {"country": "Burkina Faso", "currency": "XOF"},
    }

    print(f"\n--- Bronze Layer: Starting DB Data Ingestion from {DATA_DIRECTORY} ---")

    if not os.path.exists(DATA_DIRECTORY):
        print(f"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.")
    else:
        for filename in os.listdir(DATA_DIRECTORY):
            file_path = os.path.join(DATA_DIRECTORY, filename)
            
            # Process only files ending with '.db' and skip directories
            if os.path.isdir(file_path) or not filename.endswith(".db"):
                continue

            # Derive project_id from filename (e.g., "BE01" from "BE01.db")
            project_name_from_file = os.path.splitext(filename)[0]
            base_project_id = project_name_from_file # For DBs, the filename directly is the project_id

            # Get country and currency from our PROJECT_CURRENCY_MAP
            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)
            if not project_info:
                print(f"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.")
                continue
            
            country_from_map = project_info['country']
            currency_from_map = project_info['currency'] # This will be our source of truth for currency

            print(f"  Ingesting expenses from DB: {filename} (Project: {base_project_id}, Country: {country_from_map})...")
            df = extract_expenses_from_db(file_path)
            
            if not df.empty:
                df['project_id'] = base_project_id
                df['country'] = country_from_map
                df['original_currency'] = currency_from_map # Add the true currency from our map
                all_expenses_df = pd.concat([all_expenses_df, df], ignore_index=True)
                db_files_found += 1
            else:
                print(f"   No data extracted or error occurred for {filename}.")

    return all_expenses_df


df = bronze_expenses()


df.head()




print("Hello")

# USe the bronze_layer to get expenses df and budget df
import pandas as pd
import sqlite3
import os
from bronze_layer import bronze_expenses, bronze_budget

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

expenses_df = bronze_expenses()
budget_df = bronze_budget()

display(expenses_df.head())
budget_df.head()

# USe the bronze_layer to get expenses df and budget df
import pandas as pd
import sqlite3
import os
from bronze_layer import bronze_expenses, bronze_budget

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

expenses_df = bronze_expenses()
budget_df = bronze_budget()

expenses_df.info()

budget_df.info()

# modify KEO1 to KE01 in bronze budget df
budget_df['project_id'] = budget_df['project_id'].replace('KEO1', 'KE01')
budget_df.info()

import pandas as pd
from pandas.tseries.offsets import MonthEnd

# Create a date column:
# convert the 'year' and 'month' columns to string format
budget_df['year'] = budget_df['year'].astype(str)
budget_df['month'] = budget_df['month'].astype(str)

# create a temporary date string with the 1st day of the month
budget_df['temp_date_str'] = budget_df['year'] + '-' + budget_df['month'].str.zfill(2) + '-01'

# convert the temporary date string to datetime objects
budget_df['date'] = pd.to_datetime(budget_df['temp_date_str'], errors='coerce')

# convert to the last day of the month using MonthEnd
budget_df['date'] = budget_df['date'] + MonthEnd(1)

# drop the intermediate columns if no longer needed
budget_df = budget_df.drop(columns=['year', 'month', 'temp_date_str'])









budget_df.info()

import pandas as pd
import sqlite3
import os

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

def bronze_budget()-> pd.DataFrame:
    """Returns a dataframe containing all budget data from all CSVs."""

    # This dictionary contains the TRUTH for project country and currency,
    # overriding any inconsistencies found in source data.
    PROJECT_CURRENCY_MAP = {
        "BE01": {"country": "Belgium", "currency": "EUR"},
        "BE55": {"country": "Belgium", "currency": "EUR"},
        "KE01": {"country": "Kenya", "currency": "KES"},   # Corrected: Force KES for KE01 expenses, overriding DB's XOF
        "KEO2": {"country": "Kenya", "currency": "KES"},
        "SN01": {"country": "Senegal", "currency": "XOF"}, # Corrected: Force Senegal for SN01 country, overriding DB's Kenya
        "SN02": {"country": "Senegal", "currency": "XOF"},
        "BF01": {"country": "Burkina Faso", "currency": "XOF"},
        "BF02": {"country": "Burkina Faso", "currency": "XOF"},
    }

    DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
    OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"

    # Read csvs (budget data)
    import pandas as pd
    import os

    # --- Function to extract budget from a single CSV ---
    def extract_budget_from_csv(csv_path: str) -> pd.DataFrame:
        """
        Reads budget data from a CSV file.
        """
        try:
            df = pd.read_csv(csv_path)
            return df
        except Exception as e:
            print(f"Error reading budget from {csv_path}: {e}")
            return pd.DataFrame()
        
    # --- Main logic to iterate and combine CSVs ---
    all_budget_df = pd.DataFrame()
    csv_files_found = 0

    print(f"\n--- Bronze Layer: Starting CSV Data Ingestion from {DATA_DIRECTORY} ---")

    if not os.path.exists(DATA_DIRECTORY):
        print(f"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.")
    else:
        for filename in os.listdir(DATA_DIRECTORY):
            file_path = os.path.join(DATA_DIRECTORY, filename)
            
            # Process only files ending with '_budget.csv' and skip directories
            if os.path.isdir(file_path) or not filename.endswith("_budget.csv"):
                continue

            # Derive project_id from filename (e.g., "BE01" from "BE01_budget.csv")
            project_name_from_file = os.path.splitext(filename)[0]
            print(f"project_name_from_file: {project_name_from_file}")
            base_project_id = project_name_from_file.replace('_budget', '')

            # Get country from our PROJECT_CURRENCY_MAP
            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)
            if not project_info:
                print(f"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.")
                continue
            
            country_from_map = project_info['country']

            print(f"  Ingesting budget from CSV: {filename} (Project: {base_project_id}, Country: {country_from_map})...")
            df = extract_budget_from_csv(file_path)
            # print(f"df: \n{df.head()}")
            
            if not df.empty:
                df['project_id'] = base_project_id
                df['country'] = country_from_map
                # Also add the original_currency_map for consistency, even if budget is already in EUR
                all_budget_df = pd.concat([all_budget_df, df], ignore_index=True)
                csv_files_found += 1
            else:
                print(f" No data extracted or error occurred for {filename}.")

    return all_budget_df

def bronze_expenses()-> pd.DataFrame:
    """Returns a DataFrame containing all expenses from all DBs."""
    # --- Bronze Layer: Ingesting Expenses from SQLite DBs ---
    def extract_expenses_from_db(db_path: str) -> pd.DataFrame:
        """
        Connects to a SQLite database and extracts all data from the 'expenses' table.
        """
        conn = None
        try:
            conn = sqlite3.connect(db_path)
            query = "SELECT * FROM expenses" # Assumes 'expenses' is the table name
            df = pd.read_sql_query(query, conn)
            return df
        except sqlite3.Error as e:
            print(f"Error reading expenses from {db_path}: {e}")
            return pd.DataFrame() # Return empty DataFrame on error
        finally:
            if conn:
                conn.close()


    # --- Main logic to iterate and combine DBs ---
    all_expenses_df = pd.DataFrame()
    db_files_found = 0

    # Define project currency map for expenses
    PROJECT_CURRENCY_MAP = {
        "BE01": {"country": "Belgium", "currency": "EUR"},
        "BE55": {"country": "Belgium", "currency": "EUR"},
        "KE01": {"country": "Kenya", "currency": "KES"},
        "KE02": {"country": "Kenya", "currency": "KES"},   
        "SN01": {"country": "Senegal", "currency": "XOF"},
        "SN02": {"country": "Senegal", "currency": "XOF"},
        "BF01": {"country": "Burkina Faso", "currency": "XOF"},
        "BF02": {"country": "Burkina Faso", "currency": "XOF"},
    }

    print(f"\n--- Bronze Layer: Starting DB Data Ingestion from {DATA_DIRECTORY} ---")

    if not os.path.exists(DATA_DIRECTORY):
        print(f"Error: Data directory '{DATA_DIRECTORY}' not found. Please verify the path.")
    else:
        for filename in os.listdir(DATA_DIRECTORY):
            file_path = os.path.join(DATA_DIRECTORY, filename)
            
            # Process only files ending with '.db' and skip directories
            if os.path.isdir(file_path) or not filename.endswith(".db"):
                continue

            # Derive project_id from filename (e.g., "BE01" from "BE01.db")
            project_name_from_file = os.path.splitext(filename)[0]
            base_project_id = project_name_from_file # For DBs, the filename directly is the project_id

            # Get country and currency from our PROJECT_CURRENCY_MAP
            project_info = PROJECT_CURRENCY_MAP.get(base_project_id)
            if not project_info:
                print(f"Warning: Project '{base_project_id}' (from '{filename}') not found in PROJECT_CURRENCY_MAP. Skipping.")
                continue
            
            country_from_map = project_info['country']
            currency_from_map = project_info['currency'] # This will be our source of truth for currency

            print(f"  Ingesting expenses from DB: {filename} (Project: {base_project_id}, Country: {country_from_map})...")
            df = extract_expenses_from_db(file_path)
            
            if not df.empty:
                df['project_id'] = base_project_id
                df['country'] = country_from_map
                df['original_currency'] = currency_from_map # Add the true currency from our map
                all_expenses_df = pd.concat([all_expenses_df, df], ignore_index=True)
                db_files_found += 1
            else:
                print(f"   No data extracted or error occurred for {filename}.")

    return all_expenses_df

# USe the bronze_layer to get expenses df and budget df
import pandas as pd
import sqlite3
import os
from bronze_layer import bronze_expenses, bronze_budget
from pandas.tseries.offsets import MonthEnd

DATA_DIRECTORY = "/Users/<USER>/test/MSF-test/OneDrive_1_7-4-2025"
OUTPUT_DIRECTORY = "/Users/<USER>/test/MSF-test/processed_data"


def silver_budget() -> pd.DataFrame:
    """Returns a clean budget dataframe."""
    budget_df = bronze_budget()
    # modify KEO1 to KE01 in bronze budget df
    budget_df['project_id'] = budget_df['project_id'].replace('KEO1', 'KE01')

    # drop version column, id column
    budget_df.drop(columns=['version', 'id'], inplace=True)

    # Create a date column:
    # convert the 'year' and 'month' columns to string format
    budget_df['year'] = budget_df['year'].astype(str)
    budget_df['month'] = budget_df['month'].astype(str)

    # create a temporary date string with the 1st day of the month
    budget_df['temp_date_str'] = budget_df['year'] + '-' + budget_df['month'].str.zfill(2) + '-01'

    # convert the temporary date string to datetime objects
    budget_df['date'] = pd.to_datetime(budget_df['temp_date_str'], errors='coerce')

    # convert to the last day of the month using MonthEnd
    budget_df['date'] = budget_df['date'] + MonthEnd(1)

    # drop the intermediate columns 
    budget_df = budget_df.drop(columns=['year', 'month', 'temp_date_str'])

    return budget_df

silver_budget_df = silver_budget()

silver_budget_df.info()

silver_budget_df.isnull().sum()

# Manipulating to get silver_expenses_df
expenses_df = bronze_expenses()

expenses_df.info()

# Drop id and currency columns
expenses_df.drop(columns=['id', 'currency'], inplace=True)

# Create a date column:
# convert the 'year' and 'month' columns to string format
expenses_df['year'] = expenses_df['year'].astype(str)
expenses_df['month'] = expenses_df['month'].astype(str)

# create a temporary date string with the 1st day of the month
expenses_df['temp_date_str'] = expenses_df['year'] + '-' + expenses_df['month'].str.zfill(2) + '-01'

# convert the temporary date string to datetime objects
expenses_df['date'] = pd.to_datetime(expenses_df['temp_date_str'], errors='coerce')

# convert to the last day of the month using MonthEnd
expenses_df['date'] = expenses_df['date'] + MonthEnd(1)

# drop the intermediate columns 
expenses_df = expenses_df.drop(columns=['year', 'month', 'temp_date_str'])

# create rate column



# Drop amount_eur column
expenses_df.drop(columns=['amount_eur'], inplace=True)

expenses_df.info()

# Define our rate map
from get_latest_exchange_rate import get_latest_exchange_rate

eur_to_eur_rate = 1.0 # EUR to EUR conversion rate is 1
kes_to_eur_rate = get_latest_exchange_rate("KES", "EUR")
xof_to_eur_rate = get_latest_exchange_rate("XOF", "EUR")

rate_mapping = {
    'EUR': eur_to_eur_rate,
    'KES': kes_to_eur_rate,
    'XOF': xof_to_eur_rate
}

# Add rate column
expenses_df['rate'] = expenses_df['original_currency'].map(rate_mapping)

# Add amount_eur column
expenses_df['amount_eur'] = expenses_df['amount_local'] * expenses_df['rate']


expenses_df.info()

expenses_df["amount_eur"].isnull().sum()



silver_expenses_df = expenses_df

silver_expenses_df.info()







import numpy as np
import pandas as pd
import os
from silver_layer import silver_budget, silver_expenses

# def gold():
#     """Returns a set of gold dataframes from the silver dataframes"""
#     silver_budget_df = silver_budget()
#     silver_expenses_df = silver_expenses()

silver_budget_df = silver_budget()
silver_expenses_df = silver_expenses()

silver_budget_df.info()
silver_expenses_df.info()

silver_budget_df['project_id'].unique()

# check if set of project id in expenses == set of project id in budget
print(set(silver_expenses_df['project_id'].unique()) == set(silver_budget_df['project_id'].unique()))

# # correct the issue above
# # inspect first
# print(set(silver_expenses_df['project_id'].unique()))
# print(set(silver_budget_df['project_id'].unique()))

# combine the silver_budget_df and silver_expenses_df
# with common columns: project_id, date, country, department, category

# Merge the silver_budget_df and silver_expenses_df
common_columns = ['date', 'project_id', 'country', 'department', 'category']

merged_df = pd.merge(silver_budget_df, silver_expenses_df, on=common_columns, how='inner')  # inner is default
merged_df.info()

# Create a snowflake schema from the merged_df

# DimDepartment
# Extract unique departments and create a surrogate key
dim_department = merged_df[['department']].drop_duplicates().reset_index(drop=True)
dim_department['department_key'] = dim_department.index + 1 # Start keys from 1
dim_department = dim_department[['department_key', 'department']] # Reorder columns
dim_department.rename(columns={'department': 'department_name'}, inplace=True)
print("DimDepartment head:")
print(dim_department.head())
print("\n")

# DimCategory
dim_category = merged_df[['category']].drop_duplicates().reset_index(drop=True)
dim_category['category_key'] = dim_category.index + 1
dim_category = dim_category[['category_key', 'category']]
dim_category.rename(columns={'category': 'category_name'}, inplace=True)
print("DimCategory head:")
print(dim_category.head())
print("\n")

# DimProject
dim_project = merged_df[['project_id']].drop_duplicates().reset_index(drop=True)
dim_project['project_key'] = dim_project.index + 1
dim_project = dim_project[['project_key', 'project_id']]
dim_project.rename(columns={'project_id': 'project_name'}, inplace=True) # Assuming project_id is also the name
print("DimProject head:")
print(dim_project.head())
print("\n")

# convert the merged df to csv
merged_df.to_csv('/Users/<USER>/test/MSF-test2/processed_data/merged_df.csv', index=False)

merged_df.info()

df = pd.read_csv('/Users/<USER>/test/MSF-test2/processed_data/merged_df.csv')

df.info()

df['department'].unique()

df['category'].unique()

df['category'].nunique()

df['date'].nunique()

df['country'].unique()  

df['project_id'].unique()

df['rate'].nunique()

df['amount_local'].nunique()

import pandas as pd

# Load your final Gold layer data
df = pd.read_csv('gold_df.csv')

# Convert 'date' to YYYYMMDD integer format
df['date_id'] = pd.to_datetime(df['date']).dt.strftime('%Y%m%d').astype(int)

# Build dimension tables
dim_date = df[['date_id', 'date']].drop_duplicates().reset_index(drop=True)

dim_department = df[['department']].drop_duplicates().reset_index(drop=True)
dim_department['department_id'] = dim_department.index + 1

dim_country = df[['country']].drop_duplicates().reset_index(drop=True)
dim_country['country_id'] = dim_country.index + 1

dim_category = df[['category']].drop_duplicates().reset_index(drop=True)
dim_category['category_id'] = dim_category.index + 1

dim_project = df[['project_id']].drop_duplicates().reset_index(drop=True)
dim_project['project_id_numeric'] = dim_project.index + 1

# Merge keys back into the main dataframe
df = df.merge(dim_department, on='department', how='left') \
       .merge(dim_country, on='country', how='left') \
       .merge(dim_category, on='category', how='left') \
       .merge(dim_project, on='project_id', how='left')

# Fact Table
fact_expenses = df[['date_id', 'department_id', 'country_id', 'category_id', 'project_id_numeric', 'budget_eur', 'amount_eur']]

# Save to CSVs
dim_date.to_csv('dim_date.csv', index=False)
dim_department.to_csv('dim_department.csv', index=False)
dim_country.to_csv('dim_country.csv', index=False)
dim_category.to_csv('dim_category.csv', index=False)
dim_project.to_csv('dim_project.csv', index=False)
fact_expenses.to_csv('fact_expenses.csv', index=False)


